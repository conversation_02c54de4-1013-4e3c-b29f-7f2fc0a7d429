import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Product } from './cart'

export const useProductsStore = defineStore('products', () => {
  const products = ref<Product[]>([
    {
      id: 1,
      name: '<PERSON>',
      price: 15.99,
      image: 'https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=400&h=400&fit=crop',
      description: 'Ekşi ve çıtır Granny Smith elmaları. Taze ve organik olarak yetiştirilmiştir.',
      category: 'Ekşi Elmalar',
      weight: '1 kg',
      origin: '<PERSON>par<PERSON>'
    },
    {
      id: 2,
      name: 'Red Delicious Elması',
      price: 18.50,
      image: 'https://images.unsplash.com/photo-1568702846914-96b305d2aaeb?w=400&h=400&fit=crop',
      description: 'Klasik kırmızı elma çeşidi. Ta<PERSON>ı ve sulu, çocuklar için m<PERSON>mmel.',
      category: 'Ta<PERSON><PERSON>',
      weight: '1 kg',
      origin: '<PERSON><PERSON><PERSON>'
    },
    {
      id: 3,
      name: 'Golden Delicious Elması',
      price: 16.75,
      image: 'https://images.unsplash.com/photo-1619546813926-a78fa6372cd2?w=400&h=400&fit=crop',
      description: 'Altın sarısı rengiyle göz alıcı, bal tadında tatlı elmalar.',
      category: 'Tatlı Elmalar',
      weight: '1 kg',
      origin: 'Isparta'
    },
    {
      id: 4,
      name: 'Fuji Elması',
      price: 19.99,
      image: 'https://images.unsplash.com/photo-1576179635662-9d1983e97e1e?w=400&h=400&fit=crop',
      description: 'Japon kökenli, çok tatlı ve çıtır Fuji elmaları. Premium kalite.',
      category: 'Premium Elmalar',
      weight: '1 kg',
      origin: 'Karaman'
    },
    {
      id: 5,
      name: 'Gala Elması',
      price: 17.25,
      image: 'https://images.unsplash.com/photo-1567306226416-28f0efdc88ce?w=400&h=400&fit=crop',
      description: 'Kırmızı çizgili, orta büyüklükte tatlı elmalar. Çok popüler çeşit.',
      category: 'Tatlı Elmalar',
      weight: '1 kg',
      origin: 'Niğde'
    },
    {
      id: 6,
      name: 'Braeburn Elması',
      price: 20.50,
      image: 'https://images.unsplash.com/photo-1590005354167-6da97870c757?w=400&h=400&fit=crop',
      description: 'Tatlı ve ekşi karışımı lezzet. Pişirme için de mükemmel.',
      category: 'Karışık Lezzet',
      weight: '1 kg',
      origin: 'Isparta'
    },
    {
      id: 7,
      name: 'Honeycrisp Elması',
      price: 22.99,
      image: 'https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=400&h=400&fit=crop',
      description: 'Son derece çıtır ve bal tadında. Premium kalite organik elmalar.',
      category: 'Premium Elmalar',
      weight: '1 kg',
      origin: 'Amasya'
    },
    {
      id: 8,
      name: 'Pink Lady Elması',
      price: 24.75,
      image: 'https://images.unsplash.com/photo-1589217157232-464b505b197f?w=400&h=400&fit=crop',
      description: 'Pembe renkli, çok özel lezzet. Dünyaca ünlü premium elma çeşidi.',
      category: 'Premium Elmalar',
      weight: '1 kg',
      origin: 'Antalya'
    }
  ])

  const categories = ref([
    'Tümü',
    'Tatlı Elmalar',
    'Ekşi Elmalar',
    'Premium Elmalar',
    'Karışık Lezzet'
  ])

  const selectedCategory = ref('Tümü')
  const searchQuery = ref('')

  // Computed properties
  const filteredProducts = computed(() => {
    let filtered = products.value

    // Filter by category
    if (selectedCategory.value !== 'Tümü') {
      filtered = filtered.filter(product => product.category === selectedCategory.value)
    }

    // Filter by search query
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase()
      filtered = filtered.filter(product => 
        product.name.toLowerCase().includes(query) ||
        product.description.toLowerCase().includes(query) ||
        product.origin.toLowerCase().includes(query)
      )
    }

    return filtered
  })

  const featuredProducts = computed(() => {
    return products.value.filter(product => product.category === 'Premium Elmalar').slice(0, 3)
  })

  const productsByCategory = computed(() => {
    const grouped: { [key: string]: Product[] } = {}
    
    categories.value.forEach(category => {
      if (category !== 'Tümü') {
        grouped[category] = products.value.filter(product => product.category === category)
      }
    })
    
    return grouped
  })

  // Actions
  const setCategory = (category: string) => {
    selectedCategory.value = category
  }

  const setSearchQuery = (query: string) => {
    searchQuery.value = query
  }

  const getProductById = (id: number): Product | undefined => {
    return products.value.find(product => product.id === id)
  }

  const getProductsByCategory = (category: string): Product[] => {
    if (category === 'Tümü') {
      return products.value
    }
    return products.value.filter(product => product.category === category)
  }

  return {
    products,
    categories,
    selectedCategory,
    searchQuery,
    filteredProducts,
    featuredProducts,
    productsByCategory,
    setCategory,
    setSearchQuery,
    getProductById,
    getProductsByCategory
  }
})
