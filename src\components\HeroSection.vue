<template>
  <section class="hero parallax" ref="heroRef">
    <div class="hero-background">
      <div class="hero-image"></div>
      <div class="hero-overlay"></div>
    </div>
    
    <div class="container">
      <div class="hero-content">
        <div class="hero-text">
          <h1 class="hero-title fade-in" ref="titleRef">
            <PERSON><PERSON><PERSON>
          </h1>
          <p class="hero-subtitle fade-in" ref="subtitleRef">
            Doğanın en taze ve lezzetli elmalarını kapınıza kadar getiriyoruz
          </p>
          <p class="hero-description fade-in" ref="descriptionRef">
            Organik tarım yöntemleriyle yet<PERSON>şti<PERSON>en, vitamin ve mineral açısından zengin elmalarımızla sağlıklı yaşamın tadını çıkarın.
          </p>
          <div class="hero-buttons fade-in" ref="buttonsRef">
            <RouterLink to="/products" class="btn btn-primary">
              Ürünleri İncele
            </RouterLink>
            <RouterLink to="/about" class="btn btn-secondary">
              Hakkımızda
            </RouterLink>
          </div>
        </div>
        
        <div class="hero-stats">
          <div class="stat-item scale-in" ref="stat1Ref">
            <div class="stat-number">
              <AnimatedCounter :target="100" suffix="+" />
            </div>
            <div class="stat-label">Mutlu Müşteri</div>
          </div>
          <div class="stat-item scale-in" ref="stat2Ref">
            <div class="stat-number">
              <AnimatedCounter :target="8" />
            </div>
            <div class="stat-label">Elma Çeşidi</div>
          </div>
          <div class="stat-item scale-in" ref="stat3Ref">
            <div class="stat-number">
              <AnimatedCounter :target="5" />
            </div>
            <div class="stat-label">Yıllık Deneyim</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Floating Elements -->
    <div class="floating-elements">
      <div class="floating-apple apple-1">🍎</div>
      <div class="floating-apple apple-2">🍏</div>
      <div class="floating-apple apple-3">🍎</div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { gsap } from 'gsap'
import AnimatedCounter from './AnimatedCounter.vue'

const heroRef = ref<HTMLElement>()
const titleRef = ref<HTMLElement>()
const subtitleRef = ref<HTMLElement>()
const descriptionRef = ref<HTMLElement>()
const buttonsRef = ref<HTMLElement>()
const stat1Ref = ref<HTMLElement>()
const stat2Ref = ref<HTMLElement>()
const stat3Ref = ref<HTMLElement>()

onMounted(() => {
  // Hero entrance animation
  const tl = gsap.timeline()
  
  tl.from(titleRef.value, {
    duration: 1,
    y: 50,
    opacity: 0,
    ease: 'power2.out'
  })
  .from(subtitleRef.value, {
    duration: 0.8,
    y: 30,
    opacity: 0,
    ease: 'power2.out'
  }, '-=0.5')
  .from(descriptionRef.value, {
    duration: 0.8,
    y: 30,
    opacity: 0,
    ease: 'power2.out'
  }, '-=0.3')
  .from(buttonsRef.value, {
    duration: 0.8,
    y: 30,
    opacity: 0,
    ease: 'power2.out'
  }, '-=0.3')
  .from([stat1Ref.value, stat2Ref.value, stat3Ref.value], {
    duration: 0.6,
    scale: 0,
    opacity: 0,
    ease: 'back.out(1.7)',
    stagger: 0.2
  }, '-=0.5')

  // Floating apples animation
  gsap.to('.apple-1', {
    duration: 3,
    y: -20,
    rotation: 10,
    repeat: -1,
    yoyo: true,
    ease: 'power2.inOut'
  })
  
  gsap.to('.apple-2', {
    duration: 4,
    y: -30,
    rotation: -15,
    repeat: -1,
    yoyo: true,
    ease: 'power2.inOut',
    delay: 1
  })
  
  gsap.to('.apple-3', {
    duration: 3.5,
    y: -25,
    rotation: 8,
    repeat: -1,
    yoyo: true,
    ease: 'power2.inOut',
    delay: 2
  })

  // Parallax effect
  const handleScroll = () => {
    const scrolled = window.pageYOffset
    const parallax = heroRef.value?.querySelector('.hero-image') as HTMLElement
    if (parallax) {
      parallax.style.transform = `translateY(${scrolled * 0.5}px)`
    }
  }

  window.addEventListener('scroll', handleScroll)
})
</script>

<style scoped>
.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;
  margin-top: 80px; /* Account for fixed navbar */
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -2;
}

.hero-image {
  position: absolute;
  top: -20%;
  left: 0;
  right: 0;
  bottom: -20%;
  background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(243, 156, 18, 0.1)),
              url('https://images.unsplash.com/photo-**********-1e4cd0b6cbd6?w=1920&h=1080&fit=crop') center/cover;
  will-change: transform;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  z-index: -1;
}

.hero-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 4rem;
  align-items: center;
  min-height: 60vh;
}

.hero-text {
  max-width: 600px;
}

.hero-title {
  font-size: clamp(3rem, 6vw, 5rem);
  font-weight: 800;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.1;
}

.hero-subtitle {
  font-size: clamp(1.2rem, 2.5vw, 1.8rem);
  color: var(--text-dark);
  margin-bottom: 1.5rem;
  font-weight: 600;
}

.hero-description {
  font-size: 1.1rem;
  color: var(--text-dark);
  margin-bottom: 2.5rem;
  line-height: 1.7;
  opacity: 0.8;
}

.hero-buttons {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.hero-stats {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.stat-item {
  text-align: center;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.9);
  border-radius: var(--border-radius);
  box-shadow: 0 4px 20px var(--shadow-light);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.9rem;
  color: var(--text-dark);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: -1;
}

.floating-apple {
  position: absolute;
  font-size: 3rem;
  opacity: 0.3;
}

.apple-1 {
  top: 20%;
  right: 10%;
}

.apple-2 {
  top: 60%;
  left: 5%;
}

.apple-3 {
  top: 40%;
  right: 20%;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }
  
  .hero-stats {
    flex-direction: row;
    justify-content: center;
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .hero {
    margin-top: 70px;
    min-height: 80vh;
  }
  
  .hero-buttons {
    justify-content: center;
  }
  
  .hero-stats {
    flex-direction: column;
    gap: 1rem;
  }
  
  .stat-item {
    padding: 1rem;
  }
  
  .stat-number {
    font-size: 2rem;
  }
  
  .floating-apple {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .btn {
    width: 100%;
    max-width: 250px;
  }
}
</style>
