<template>
  <span>{{ displayValue }}{{ suffix }}</span>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { gsap } from 'gsap'

interface Props {
  target: number
  duration?: number
  suffix?: string
  prefix?: string
  decimals?: number
  startOnMount?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  duration: 2,
  suffix: '',
  prefix: '',
  decimals: 0,
  startOnMount: true
})

const displayValue = ref(0)
const animationObject = ref({ value: 0 })

const formatNumber = (num: number): string => {
  const formatted = num.toFixed(props.decimals)
  return props.prefix + formatted
}

const animateCounter = () => {
  gsap.to(animationObject.value, {
    duration: props.duration,
    value: props.target,
    ease: 'power2.out',
    onUpdate: () => {
      displayValue.value = Math.floor(animationObject.value.value)
    }
  })
}

const startAnimation = () => {
  animationObject.value.value = 0
  displayValue.value = 0
  animateCounter()
}

// Watch for target changes
watch(() => props.target, () => {
  startAnimation()
})

onMounted(() => {
  if (props.startOnMount) {
    // Add a small delay to make the animation more noticeable
    setTimeout(() => {
      startAnimation()
    }, 500)
  }
})

// Expose the start function for manual triggering
defineExpose({
  start: startAnimation
})
</script>

<style scoped>
span {
  display: inline-block;
  font-variant-numeric: tabular-nums;
}
</style>
