(function(i,o){"use strict";function $(t,e){var n;const r=o.shallowRef();return o.watchEffect(()=>{r.value=t()},{...e,flush:(n=e?.flush)!=null?n:"sync"}),o.readonly(r)}function H(t,e){let n,r,a;const l=o.shallowRef(!0),c=()=>{l.value=!0,a()};o.watch(t,c,{flush:"sync"});const u=typeof e=="function"?e:e.get,s=typeof e=="function"?void 0:e.set,h=o.customRef((m,d)=>(r=m,a=d,{get(){return l.value&&(n=u(n),l.value=!1),r(),n},set(f){s?.(f)}}));return Object.isExtensible(h)&&(h.trigger=c),h}function p(t){return o.getCurrentScope()?(o.onScopeDispose(t),!0):!1}function dt(){const t=new Set,e=l=>{t.delete(l)};return{on:l=>{t.add(l);const c=()=>e(l);return p(c),{off:c}},off:e,trigger:(...l)=>Promise.all(Array.from(t).map(c=>c(...l))),clear:()=>{t.clear()}}}function mt(t){let e=!1,n;const r=o.effectScope(!0);return(...a)=>(e||(n=r.run(()=>t(...a)),e=!0),n)}const S=new WeakMap,G=(...t)=>{var e;const n=t[0],r=(e=o.getCurrentInstance())==null?void 0:e.proxy;if(r==null&&!o.hasInjectionContext())throw new Error("injectLocal must be called in setup");return r&&S.has(r)&&n in S.get(r)?S.get(r)[n]:o.inject(...t)};function Z(t,e){var n;const r=(n=o.getCurrentInstance())==null?void 0:n.proxy;if(r==null)throw new Error("provideLocal must be called in setup");S.has(r)||S.set(r,Object.create(null));const a=S.get(r);return a[t]=e,o.provide(t,e)}function ht(t,e){const n=e?.injectionKey||Symbol(t.name||"InjectionState"),r=e?.defaultValue;return[(...c)=>{const u=t(...c);return Z(n,u),u},()=>G(n,r)]}function yt(t,e){return e===!0?o.ref(t):o.shallowRef(t)}function wt(t){let e=0,n,r;const a=()=>{e-=1,r&&e<=0&&(r.stop(),n=void 0,r=void 0)};return(...l)=>(e+=1,r||(r=o.effectScope(!0),n=r.run(()=>t(...l))),p(a),n)}function q(t,e,{enumerable:n=!1,unwrap:r=!0}={}){for(const[a,l]of Object.entries(e))a!=="value"&&(o.isRef(l)&&r?Object.defineProperty(t,a,{get(){return l.value},set(c){l.value=c},enumerable:n}):Object.defineProperty(t,a,{value:l,enumerable:n}));return t}function gt(t,e){return e==null?o.unref(t):o.unref(t)[e]}function Vt(t){return o.unref(t)!=null}function bt(t,e){if(typeof Symbol<"u"){const n={...t};return Object.defineProperty(n,Symbol.iterator,{enumerable:!1,value(){let r=0;return{next:()=>({value:e[r++],done:r>e.length})}}}),n}else return Object.assign([...e],t)}function _(t,e){const n=e?.computedGetter===!1?o.unref:o.toValue;return function(...r){return o.computed(()=>t.apply(this,r.map(a=>n(a))))}}function pt(t,e={}){let n=[],r;if(Array.isArray(e))n=e;else{r=e;const{includeOwnProperties:a=!0}=e;n.push(...Object.keys(t)),a&&n.push(...Object.getOwnPropertyNames(t))}return Object.fromEntries(n.map(a=>{const l=t[a];return[a,typeof l=="function"?_(l.bind(t),r):l]}))}function J(t){if(!o.isRef(t))return o.reactive(t);const e=new Proxy({},{get(n,r,a){return o.unref(Reflect.get(t.value,r,a))},set(n,r,a){return o.isRef(t.value[r])&&!o.isRef(a)?t.value[r].value=a:t.value[r]=a,!0},deleteProperty(n,r){return Reflect.deleteProperty(t.value,r)},has(n,r){return Reflect.has(t.value,r)},ownKeys(){return Object.keys(t.value)},getOwnPropertyDescriptor(){return{enumerable:!0,configurable:!0}}});return o.reactive(e)}function N(t){return J(o.computed(t))}function At(t,...e){const n=e.flat(),r=n[0];return N(()=>Object.fromEntries(typeof r=="function"?Object.entries(o.toRefs(t)).filter(([a,l])=>!r(o.toValue(l),a)):Object.entries(o.toRefs(t)).filter(a=>!n.includes(a[0]))))}const D=typeof window<"u"&&typeof document<"u",Ot=typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope,St=t=>typeof t<"u",Rt=t=>t!=null,Tt=(t,...e)=>{t||console.warn(...e)},Dt=Object.prototype.toString,X=t=>Dt.call(t)==="[object Object]",Ft=()=>Date.now(),K=()=>+Date.now(),Mt=(t,e,n)=>Math.min(n,Math.max(e,t)),A=()=>{},Pt=(t,e)=>(t=Math.ceil(t),e=Math.floor(e),Math.floor(Math.random()*(e-t+1))+t),It=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),Ct=kt();function kt(){var t,e;return D&&((t=window?.navigator)==null?void 0:t.userAgent)&&(/iP(?:ad|hone|od)/.test(window.navigator.userAgent)||((e=window?.navigator)==null?void 0:e.maxTouchPoints)>2&&/iPad|Macintosh/.test(window?.navigator.userAgent))}function I(...t){if(t.length!==1)return o.toRef(...t);const e=t[0];return typeof e=="function"?o.readonly(o.customRef(()=>({get:e,set:A}))):o.ref(e)}const Et=I;function _t(t,...e){const n=e.flat(),r=n[0];return N(()=>Object.fromEntries(typeof r=="function"?Object.entries(o.toRefs(t)).filter(([a,l])=>r(o.toValue(l),a)):n.map(a=>[a,I(t,a)])))}function Q(t,e=1e4){return o.customRef((n,r)=>{let a=o.toValue(t),l;const c=()=>setTimeout(()=>{a=o.toValue(t),r()},o.toValue(e));return p(()=>{clearTimeout(l)}),{get(){return n(),a},set(u){a=u,r(),clearTimeout(l),l=c()}}})}function F(t,e){function n(...r){return new Promise((a,l)=>{Promise.resolve(t(()=>e.apply(this,r),{fn:e,thisArg:this,args:r})).then(a).catch(l)})}return n}const C=t=>t();function L(t,e={}){let n,r,a=A;const l=s=>{clearTimeout(s),a(),a=A};let c;return s=>{const h=o.toValue(t),m=o.toValue(e.maxWait);return n&&l(n),h<=0||m!==void 0&&m<=0?(r&&(l(r),r=null),Promise.resolve(s())):new Promise((d,f)=>{a=e.rejectOnCancel?f:d,c=s,m&&!r&&(r=setTimeout(()=>{n&&l(n),r=null,d(c())},m)),n=setTimeout(()=>{r&&l(r),r=null,d(s())},h)})}}function j(...t){let e=0,n,r=!0,a=A,l,c,u,s,h;!o.isRef(t[0])&&typeof t[0]=="object"?{delay:c,trailing:u=!0,leading:s=!0,rejectOnCancel:h=!1}=t[0]:[c,u=!0,s=!0,h=!1]=t;const m=()=>{n&&(clearTimeout(n),n=void 0,a(),a=A)};return f=>{const y=o.toValue(c),g=Date.now()-e,V=()=>l=f();return m(),y<=0?(e=Date.now(),V()):(g>y&&(s||!r)?(e=Date.now(),V()):u&&(l=new Promise((w,b)=>{a=h?b:w,n=setTimeout(()=>{e=Date.now(),r=!0,w(V()),m()},Math.max(0,y-g))})),!s&&!n&&(n=setTimeout(()=>r=!0,y)),r=!1,l)}}function v(t=C,e={}){const{initialState:n="active"}=e,r=I(n==="active");function a(){r.value=!1}function l(){r.value=!0}const c=(...u)=>{r.value&&t(...u)};return{isActive:o.readonly(r),pause:a,resume:l,eventFilter:c}}function W(t,e=!1,n="Timeout"){return new Promise((r,a)=>{setTimeout(e?()=>a(n):r,t)})}function Nt(t){return t}function Lt(t){let e;function n(){return e||(e=t()),e}return n.reset=async()=>{const r=e;e=void 0,r&&await r},n}function jt(t){return t()}function x(t,...e){return e.some(n=>n in t)}function Wt(t,e){var n;if(typeof t=="number")return t+e;const r=((n=t.match(/^-?\d+\.?\d*/))==null?void 0:n[0])||"",a=t.slice(r.length),l=Number.parseFloat(r)+e;return Number.isNaN(l)?t:l+a}function Ut(t){return t.endsWith("rem")?Number.parseFloat(t)*16:Number.parseFloat(t)}function zt(t,e,n=!1){return e.reduce((r,a)=>(a in t&&(!n||t[a]!==void 0)&&(r[a]=t[a]),r),{})}function Bt(t,e,n=!1){return Object.fromEntries(Object.entries(t).filter(([r,a])=>(!n||a!==void 0)&&!e.includes(r)))}function Yt(t){return Object.entries(t)}function tt(t){return Array.isArray(t)?t:[t]}function et(t){const e=Object.create(null);return n=>e[n]||(e[n]=t(n))}const $t=/\B([A-Z])/g,Ht=et(t=>t.replace($t,"-$1").toLowerCase()),Gt=/-(\w)/g,Zt=et(t=>t.replace(Gt,(e,n)=>n?n.toUpperCase():""));function M(t){return t||o.getCurrentInstance()}function nt(t,e=200,n={}){return F(L(e,n),t)}function U(t,e=200,n={}){const r=o.ref(o.toValue(t)),a=nt(()=>{r.value=t.value},e,n);return o.watch(t,()=>a()),o.shallowReadonly(r)}function qt(t,e){return o.computed({get(){var n;return(n=t.value)!=null?n:e},set(n){t.value=n}})}function rt(t,e=200,n=!1,r=!0,a=!1){return F(j(e,n,r,a),t)}function z(t,e=200,n=!0,r=!0){if(e<=0)return t;const a=o.ref(o.toValue(t)),l=rt(()=>{a.value=t.value},e,n,r);return o.watch(t,()=>l()),a}function ot(t,e={}){let n=t,r,a;const l=o.customRef((f,y)=>(r=f,a=y,{get(){return c()},set(g){u(g)}}));function c(f=!0){return f&&r(),n}function u(f,y=!0){var g,V;if(f===n)return;const w=n;((g=e.onBeforeChange)==null?void 0:g.call(e,f,w))!==!1&&(n=f,(V=e.onChanged)==null||V.call(e,f,w),y&&a())}return q(l,{get:c,set:u,untrackedGet:()=>c(!1),silentSet:f=>u(f,!1),peek:()=>c(!1),lay:f=>u(f,!1)},{enumerable:!0})}const Jt=ot;function Xt(...t){if(t.length===2){const[e,n]=t;e.value=n}if(t.length===3){const[e,n,r]=t;e[n]=r}}function P(t,e,n={}){const{eventFilter:r=C,...a}=n;return o.watch(t,F(r,e),a)}function k(t,e,n={}){const{eventFilter:r,initialState:a="active",...l}=n,{eventFilter:c,pause:u,resume:s,isActive:h}=v(r,{initialState:a});return{stop:P(t,e,{...l,eventFilter:c}),pause:u,resume:s,isActive:h}}function Kt(t,e,...[n]){const{flush:r="sync",deep:a=!1,immediate:l=!0,direction:c="both",transform:u={}}=n||{},s=[],h="ltr"in u&&u.ltr||(f=>f),m="rtl"in u&&u.rtl||(f=>f);return(c==="both"||c==="ltr")&&s.push(k(t,f=>{s.forEach(y=>y.pause()),e.value=h(f),s.forEach(y=>y.resume())},{flush:r,deep:a,immediate:l})),(c==="both"||c==="rtl")&&s.push(k(e,f=>{s.forEach(y=>y.pause()),t.value=m(f),s.forEach(y=>y.resume())},{flush:r,deep:a,immediate:l})),()=>{s.forEach(f=>f.stop())}}function Qt(t,e,n={}){const{flush:r="sync",deep:a=!1,immediate:l=!0}=n,c=tt(e);return o.watch(t,u=>c.forEach(s=>s.value=u),{flush:r,deep:a,immediate:l})}function vt(t,e={}){if(!o.isRef(t))return o.toRefs(t);const n=Array.isArray(t.value)?Array.from({length:t.value.length}):{};for(const r in t.value)n[r]=o.customRef(()=>({get(){return t.value[r]},set(a){var l;if((l=o.toValue(e.replaceRef))!=null?l:!0)if(Array.isArray(t.value)){const u=[...t.value];u[r]=a,t.value=u}else{const u={...t.value,[r]:a};Object.setPrototypeOf(u,Object.getPrototypeOf(t.value)),t.value=u}else t.value[r]=a}}));return n}const xt=o.toValue,te=o.toValue;function ee(t,e=!0,n){M(n)?o.onBeforeMount(t,n):e?t():o.nextTick(t)}function ne(t,e){M(e)&&o.onBeforeUnmount(t,e)}function re(t,e=!0,n){M(n)?o.onMounted(t,n):e?t():o.nextTick(t)}function oe(t,e){M(e)&&o.onUnmounted(t,e)}function B(t,e=!1){function n(d,{flush:f="sync",deep:y=!1,timeout:g,throwOnTimeout:V}={}){let w=null;const R=[new Promise(E=>{w=o.watch(t,T=>{d(T)!==e&&(w?w():o.nextTick(()=>w?.()),E(T))},{flush:f,deep:y,immediate:!0})})];return g!=null&&R.push(W(g,V).then(()=>o.toValue(t)).finally(()=>w?.())),Promise.race(R)}function r(d,f){if(!o.isRef(d))return n(T=>T===d,f);const{flush:y="sync",deep:g=!1,timeout:V,throwOnTimeout:w}=f??{};let b=null;const E=[new Promise(T=>{b=o.watch([t,d],([ft,$e])=>{e!==(ft===$e)&&(b?b():o.nextTick(()=>b?.()),T(ft))},{flush:y,deep:g,immediate:!0})})];return V!=null&&E.push(W(V,w).then(()=>o.toValue(t)).finally(()=>(b?.(),o.toValue(t)))),Promise.race(E)}function a(d){return n(f=>!!f,d)}function l(d){return r(null,d)}function c(d){return r(void 0,d)}function u(d){return n(Number.isNaN,d)}function s(d,f){return n(y=>{const g=Array.from(y);return g.includes(d)||g.includes(o.toValue(d))},f)}function h(d){return m(1,d)}function m(d=1,f){let y=-1;return n(()=>(y+=1,y>=d),f)}return Array.isArray(o.toValue(t))?{toMatch:n,toContains:s,changed:h,changedTimes:m,get not(){return B(t,!e)}}:{toMatch:n,toBe:r,toBeTruthy:a,toBeNull:l,toBeNaN:u,toBeUndefined:c,changed:h,changedTimes:m,get not(){return B(t,!e)}}}function ae(t){return B(t)}function le(t,e){return t===e}function ie(...t){var e,n;const r=t[0],a=t[1];let l=(e=t[2])!=null?e:le;const{symmetric:c=!1}=(n=t[3])!=null?n:{};if(typeof l=="string"){const s=l;l=(h,m)=>h[s]===m[s]}const u=o.computed(()=>o.toValue(r).filter(s=>o.toValue(a).findIndex(h=>l(s,h))===-1));if(c){const s=o.computed(()=>o.toValue(a).filter(h=>o.toValue(r).findIndex(m=>l(h,m))===-1));return o.computed(()=>c?[...o.toValue(u),...o.toValue(s)]:o.toValue(u))}else return u}function ce(t,e){return o.computed(()=>o.toValue(t).every((n,r,a)=>e(o.toValue(n),r,a)))}function ue(t,e){return o.computed(()=>o.toValue(t).map(n=>o.toValue(n)).filter(e))}function se(t,e){return o.computed(()=>o.toValue(o.toValue(t).find((n,r,a)=>e(o.toValue(n),r,a))))}function fe(t,e){return o.computed(()=>o.toValue(t).findIndex((n,r,a)=>e(o.toValue(n),r,a)))}function de(t,e){let n=t.length;for(;n-- >0;)if(e(t[n],n,t))return t[n]}function me(t,e){return o.computed(()=>o.toValue(Array.prototype.findLast?o.toValue(t).findLast((n,r,a)=>e(o.toValue(n),r,a)):de(o.toValue(t),(n,r,a)=>e(o.toValue(n),r,a))))}function he(t){return X(t)&&x(t,"formIndex","comparator")}function ye(...t){var e;const n=t[0],r=t[1];let a=t[2],l=0;if(he(a)&&(l=(e=a.fromIndex)!=null?e:0,a=a.comparator),typeof a=="string"){const c=a;a=(u,s)=>u[c]===o.toValue(s)}return a=a??((c,u)=>c===o.toValue(u)),o.computed(()=>o.toValue(n).slice(l).some((c,u,s)=>a(o.toValue(c),o.toValue(r),u,o.toValue(s))))}function we(t,e){return o.computed(()=>o.toValue(t).map(n=>o.toValue(n)).join(o.toValue(e)))}function ge(t,e){return o.computed(()=>o.toValue(t).map(n=>o.toValue(n)).map(e))}function Ve(t,e,...n){const r=(a,l,c)=>e(o.toValue(a),o.toValue(l),c);return o.computed(()=>{const a=o.toValue(t);return n.length?a.reduce(r,typeof n[0]=="function"?o.toValue(n[0]()):o.toValue(n[0])):a.reduce(r)})}function be(t,e){return o.computed(()=>o.toValue(t).some((n,r,a)=>e(o.toValue(n),r,a)))}function pe(t){return Array.from(new Set(t))}function Ae(t,e){return t.reduce((n,r)=>(n.some(a=>e(r,a,t))||n.push(r),n),[])}function Oe(t,e){return o.computed(()=>{const n=o.toValue(t).map(r=>o.toValue(r));return e?Ae(n,e):pe(n)})}function Se(t=0,e={}){let n=o.unref(t);const r=o.shallowRef(t),{max:a=Number.POSITIVE_INFINITY,min:l=Number.NEGATIVE_INFINITY}=e,c=(d=1)=>r.value=Math.max(Math.min(a,r.value+d),l),u=(d=1)=>r.value=Math.min(Math.max(l,r.value-d),a),s=()=>r.value,h=d=>r.value=Math.max(l,Math.min(a,d)),m=(d=n)=>(n=d,h(d));return{count:o.shallowReadonly(r),inc:c,dec:u,get:s,set:h,reset:m}}const Re=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[T\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/i,Te=/[YMDHhms]o|\[([^\]]+)\]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a{1,2}|A{1,2}|m{1,2}|s{1,2}|Z{1,2}|z{1,4}|SSS/g;function De(t,e,n,r){let a=t<12?"AM":"PM";return r&&(a=a.split("").reduce((l,c)=>l+=`${c}.`,"")),n?a.toLowerCase():a}function O(t){const e=["th","st","nd","rd"],n=t%100;return t+(e[(n-20)%10]||e[n]||e[0])}function at(t,e,n={}){var r;const a=t.getFullYear(),l=t.getMonth(),c=t.getDate(),u=t.getHours(),s=t.getMinutes(),h=t.getSeconds(),m=t.getMilliseconds(),d=t.getDay(),f=(r=n.customMeridiem)!=null?r:De,y=V=>{var w;return(w=V.split(" ")[1])!=null?w:""},g={Yo:()=>O(a),YY:()=>String(a).slice(-2),YYYY:()=>a,M:()=>l+1,Mo:()=>O(l+1),MM:()=>`${l+1}`.padStart(2,"0"),MMM:()=>t.toLocaleDateString(o.toValue(n.locales),{month:"short"}),MMMM:()=>t.toLocaleDateString(o.toValue(n.locales),{month:"long"}),D:()=>String(c),Do:()=>O(c),DD:()=>`${c}`.padStart(2,"0"),H:()=>String(u),Ho:()=>O(u),HH:()=>`${u}`.padStart(2,"0"),h:()=>`${u%12||12}`.padStart(1,"0"),ho:()=>O(u%12||12),hh:()=>`${u%12||12}`.padStart(2,"0"),m:()=>String(s),mo:()=>O(s),mm:()=>`${s}`.padStart(2,"0"),s:()=>String(h),so:()=>O(h),ss:()=>`${h}`.padStart(2,"0"),SSS:()=>`${m}`.padStart(3,"0"),d:()=>d,dd:()=>t.toLocaleDateString(o.toValue(n.locales),{weekday:"narrow"}),ddd:()=>t.toLocaleDateString(o.toValue(n.locales),{weekday:"short"}),dddd:()=>t.toLocaleDateString(o.toValue(n.locales),{weekday:"long"}),A:()=>f(u,s),AA:()=>f(u,s,!1,!0),a:()=>f(u,s,!0),aa:()=>f(u,s,!0,!0),z:()=>y(t.toLocaleDateString(o.toValue(n.locales),{timeZoneName:"shortOffset"})),zz:()=>y(t.toLocaleDateString(o.toValue(n.locales),{timeZoneName:"shortOffset"})),zzz:()=>y(t.toLocaleDateString(o.toValue(n.locales),{timeZoneName:"shortOffset"})),zzzz:()=>y(t.toLocaleDateString(o.toValue(n.locales),{timeZoneName:"longOffset"}))};return e.replace(Te,(V,w)=>{var b,R;return(R=w??((b=g[V])==null?void 0:b.call(g)))!=null?R:V})}function lt(t){if(t===null)return new Date(Number.NaN);if(t===void 0)return new Date;if(t instanceof Date)return new Date(t);if(typeof t=="string"&&!/Z$/i.test(t)){const e=t.match(Re);if(e){const n=e[2]-1||0,r=(e[7]||"0").substring(0,3);return new Date(e[1],n,e[3]||1,e[4]||0,e[5]||0,e[6]||0,r)}}return new Date(t)}function Fe(t,e="HH:mm:ss",n={}){return o.computed(()=>at(lt(o.toValue(t)),o.toValue(e),n))}function it(t,e=1e3,n={}){const{immediate:r=!0,immediateCallback:a=!1}=n;let l=null;const c=o.shallowRef(!1);function u(){l&&(clearInterval(l),l=null)}function s(){c.value=!1,u()}function h(){const m=o.toValue(e);m<=0||(c.value=!0,a&&t(),u(),c.value&&(l=setInterval(t,m)))}if(r&&D&&h(),o.isRef(e)||typeof e=="function"){const m=o.watch(e,()=>{c.value&&D&&h()});p(m)}return p(s),{isActive:o.shallowReadonly(c),pause:s,resume:h}}function Me(t=1e3,e={}){const{controls:n=!1,immediate:r=!0,callback:a}=e,l=o.shallowRef(0),c=()=>l.value+=1,u=()=>{l.value=0},s=it(a?()=>{c(),a(l.value)}:c,t,{immediate:r});return n?{counter:o.shallowReadonly(l),reset:u,...s}:o.shallowReadonly(l)}function Pe(t,e={}){var n;const r=o.shallowRef((n=e.initialValue)!=null?n:null);return o.watch(t,()=>r.value=K(),e),o.shallowReadonly(r)}function ct(t,e,n={}){const{immediate:r=!0,immediateCallback:a=!1}=n,l=o.shallowRef(!1);let c=null;function u(){c&&(clearTimeout(c),c=null)}function s(){l.value=!1,u()}function h(...m){a&&t(),u(),l.value=!0,c=setTimeout(()=>{l.value=!1,c=null,t(...m)},o.toValue(e))}return r&&(l.value=!0,D&&h()),p(s),{isPending:o.shallowReadonly(l),start:h,stop:s}}function Ie(t=1e3,e={}){const{controls:n=!1,callback:r}=e,a=ct(r??A,t,e),l=o.computed(()=>!a.isPending.value);return n?{ready:l,...a}:l}function Ce(t,e={}){const{method:n="parseFloat",radix:r,nanToZero:a}=e;return o.computed(()=>{let l=o.toValue(t);return typeof n=="function"?l=n(l):typeof l=="string"&&(l=Number[n](l,r)),a&&Number.isNaN(l)&&(l=0),l})}function ke(t){return o.computed(()=>`${o.toValue(t)}`)}function Ee(t=!1,e={}){const{truthyValue:n=!0,falsyValue:r=!1}=e,a=o.isRef(t),l=o.shallowRef(t);function c(u){if(arguments.length)return l.value=u,l.value;{const s=o.toValue(n);return l.value=l.value===s?o.toValue(r):s,l.value}}return a?c:[l,c]}function _e(t,e,n){let r=n?.immediate?[]:[...typeof t=="function"?t():Array.isArray(t)?t:o.toValue(t)];return o.watch(t,(a,l,c)=>{const u=Array.from({length:r.length}),s=[];for(const m of a){let d=!1;for(let f=0;f<r.length;f++)if(!u[f]&&m===r[f]){u[f]=!0,d=!0;break}d||s.push(m)}const h=r.filter((m,d)=>!u[d]);e(a,r,s,h,c),r=[...a]},n)}function Ne(t,e,n){const{count:r,...a}=n,l=o.shallowRef(0),c=P(t,(...u)=>{l.value+=1,l.value>=o.toValue(r)&&o.nextTick(()=>c()),e(...u)},a);return{count:l,stop:c}}function ut(t,e,n={}){const{debounce:r=0,maxWait:a=void 0,...l}=n;return P(t,e,{...l,eventFilter:L(r,{maxWait:a})})}function Le(t,e,n){return o.watch(t,e,{...n,deep:!0})}function Y(t,e,n={}){const{eventFilter:r=C,...a}=n,l=F(r,e);let c,u,s;if(a.flush==="sync"){const h=o.shallowRef(!1);u=()=>{},c=m=>{h.value=!0,m(),h.value=!1},s=o.watch(t,(...m)=>{h.value||l(...m)},a)}else{const h=[],m=o.shallowRef(0),d=o.shallowRef(0);u=()=>{m.value=d.value},h.push(o.watch(t,()=>{d.value++},{...a,flush:"sync"})),c=f=>{const y=d.value;f(),m.value+=d.value-y},h.push(o.watch(t,(...f)=>{const y=m.value>0&&m.value===d.value;m.value=0,d.value=0,!y&&l(...f)},a)),s=()=>{h.forEach(f=>f())}}return{stop:s,ignoreUpdates:c,ignorePrevAsyncUpdates:u}}function je(t,e,n){return o.watch(t,e,{...n,immediate:!0})}function We(t,e,n){return o.watch(t,e,{...n,once:!0})}function st(t,e,n={}){const{throttle:r=0,trailing:a=!0,leading:l=!0,...c}=n;return P(t,e,{...c,eventFilter:j(r,a,l)})}function Ue(t,e,n={}){let r;function a(){if(!r)return;const m=r;r=void 0,m()}function l(m){r=m}const c=(m,d)=>(a(),e(m,d,l)),u=Y(t,c,n),{ignoreUpdates:s}=u;return{...u,trigger:()=>{let m;return s(()=>{m=c(ze(t),Be(t))}),m}}}function ze(t){return o.isReactive(t)?t:Array.isArray(t)?t.map(e=>o.toValue(e)):o.toValue(t)}function Be(t){return Array.isArray(t)?t.map(()=>{}):void 0}function Ye(t,e,n){const r=o.watch(t,(a,l,c)=>{a&&(n?.once&&o.nextTick(()=>r()),e(a,l,c))},{...n,once:!1});return r}i.assert=Tt,i.autoResetRef=Q,i.bypassFilter=C,i.camelize=Zt,i.clamp=Mt,i.computedEager=$,i.computedWithControl=H,i.containsProp=x,i.controlledComputed=H,i.controlledRef=Jt,i.createEventHook=dt,i.createFilterWrapper=F,i.createGlobalState=mt,i.createInjectionState=ht,i.createReactiveFn=_,i.createRef=yt,i.createSharedComposable=wt,i.createSingletonPromise=Lt,i.debounceFilter=L,i.debouncedRef=U,i.debouncedWatch=ut,i.eagerComputed=$,i.extendRef=q,i.formatDate=at,i.get=gt,i.getLifeCycleTarget=M,i.hasOwn=It,i.hyphenate=Ht,i.identity=Nt,i.ignorableWatch=Y,i.increaseWithUnit=Wt,i.injectLocal=G,i.invoke=jt,i.isClient=D,i.isDef=St,i.isDefined=Vt,i.isIOS=Ct,i.isObject=X,i.isWorker=Ot,i.makeDestructurable=bt,i.noop=A,i.normalizeDate=lt,i.notNullish=Rt,i.now=Ft,i.objectEntries=Yt,i.objectOmit=Bt,i.objectPick=zt,i.pausableFilter=v,i.pausableWatch=k,i.promiseTimeout=W,i.provideLocal=Z,i.pxValue=Ut,i.rand=Pt,i.reactify=_,i.reactifyObject=pt,i.reactiveComputed=N,i.reactiveOmit=At,i.reactivePick=_t,i.refAutoReset=Q,i.refDebounced=U,i.refDefault=qt,i.refThrottled=z,i.refWithControl=ot,i.resolveRef=Et,i.resolveUnref=te,i.set=Xt,i.syncRef=Kt,i.syncRefs=Qt,i.throttleFilter=j,i.throttledRef=z,i.throttledWatch=st,i.timestamp=K,i.toArray=tt,i.toReactive=J,i.toRef=I,i.toRefs=vt,i.toValue=xt,i.tryOnBeforeMount=ee,i.tryOnBeforeUnmount=ne,i.tryOnMounted=re,i.tryOnScopeDispose=p,i.tryOnUnmounted=oe,i.until=ae,i.useArrayDifference=ie,i.useArrayEvery=ce,i.useArrayFilter=ue,i.useArrayFind=se,i.useArrayFindIndex=fe,i.useArrayFindLast=me,i.useArrayIncludes=ye,i.useArrayJoin=we,i.useArrayMap=ge,i.useArrayReduce=Ve,i.useArraySome=be,i.useArrayUnique=Oe,i.useCounter=Se,i.useDateFormat=Fe,i.useDebounce=U,i.useDebounceFn=nt,i.useInterval=Me,i.useIntervalFn=it,i.useLastChanged=Pe,i.useThrottle=z,i.useThrottleFn=rt,i.useTimeout=Ie,i.useTimeoutFn=ct,i.useToNumber=Ce,i.useToString=ke,i.useToggle=Ee,i.watchArray=_e,i.watchAtMost=Ne,i.watchDebounced=ut,i.watchDeep=Le,i.watchIgnorable=Y,i.watchImmediate=je,i.watchOnce=We,i.watchPausable=k,i.watchThrottled=st,i.watchTriggerable=Ue,i.watchWithFilter=P,i.whenever=Ye})(this.VueUse=this.VueUse||{},Vue);
