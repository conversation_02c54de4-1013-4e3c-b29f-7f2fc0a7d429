<template>
  <div class="product-card hover-lift" ref="cardRef">
    <div class="product-image-container image-overlay">
      <img :src="product.image" :alt="product.name" class="product-image" />
      <div class="product-overlay">
        <button class="quick-view-btn" @click="$emit('quickView', product)">
          👁️ Hızlı Görünüm
        </button>
      </div>
      <div class="product-badge">{{ product.category }}</div>
    </div>
    
    <div class="product-info">
      <h3 class="product-name">{{ product.name }}</h3>
      <p class="product-description">{{ product.description }}</p>
      
      <div class="product-details">
        <div class="detail-item">
          <span class="detail-label">Ağırlık:</span>
          <span class="detail-value">{{ product.weight }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">Menşei:</span>
          <span class="detail-value">{{ product.origin }}</span>
        </div>
      </div>
      
      <div class="product-footer">
        <div class="product-price">
          <span class="price-amount">{{ formatPrice(product.price) }}</span>
          <span class="price-unit">/ kg</span>
        </div>
        
        <div class="product-actions">
          <div class="quantity-selector" v-if="showQuantitySelector">
            <button class="quantity-btn" @click="decreaseQuantity" :disabled="quantity <= 1">-</button>
            <span class="quantity-display">{{ quantity }}</span>
            <button class="quantity-btn" @click="increaseQuantity">+</button>
          </div>
          
          <button 
            class="add-to-cart-btn btn btn-primary"
            @click="handleAddToCart"
            :disabled="isAdding"
            :class="{ 'adding': isAdding }"
          >
            <span v-if="!isAdding">🛒 Sepete Ekle</span>
            <span v-else class="loading-spinner"></span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { gsap } from 'gsap'
import { useCartStore } from '@/stores/cart'
import type { Product } from '@/stores/cart'

interface Props {
  product: Product
  showQuantitySelector?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showQuantitySelector: true
})

const emit = defineEmits<{
  quickView: [product: Product]
}>()

const cartStore = useCartStore()
const cardRef = ref<HTMLElement>()
const quantity = ref(1)
const isAdding = ref(false)

const formatPrice = (price: number): string => {
  return new Intl.NumberFormat('tr-TR', {
    style: 'currency',
    currency: 'TRY'
  }).format(price)
}

const increaseQuantity = () => {
  quantity.value++
}

const decreaseQuantity = () => {
  if (quantity.value > 1) {
    quantity.value--
  }
}

const handleAddToCart = async () => {
  isAdding.value = true
  
  // Add a small delay for better UX
  await new Promise(resolve => setTimeout(resolve, 500))
  
  cartStore.addToCart(props.product, quantity.value)
  
  // Success animation
  gsap.to(cardRef.value, {
    duration: 0.3,
    scale: 1.05,
    yoyo: true,
    repeat: 1,
    ease: 'power2.inOut'
  })
  
  isAdding.value = false
  
  // Reset quantity after adding
  quantity.value = 1
}

onMounted(() => {
  // Entrance animation
  gsap.from(cardRef.value, {
    duration: 0.6,
    y: 30,
    opacity: 0,
    ease: 'power2.out',
    delay: Math.random() * 0.3 // Stagger effect
  })
})
</script>

<style scoped>
.product-card {
  background: white;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: 0 4px 20px var(--shadow-light);
  transition: var(--transition-medium);
  position: relative;
}

.product-image-container {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition-medium);
}

.product-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--transition-medium);
}

.product-card:hover .product-overlay {
  opacity: 1;
}

.quick-view-btn {
  background: white;
  color: var(--text-dark);
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: var(--border-radius);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-fast);
  transform: translateY(20px);
}

.product-card:hover .quick-view-btn {
  transform: translateY(0);
}

.quick-view-btn:hover {
  background: var(--primary-color);
  color: white;
}

.product-badge {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: var(--primary-color);
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.product-info {
  padding: 1.5rem;
}

.product-name {
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 0.5rem;
}

.product-description {
  color: var(--text-dark);
  opacity: 0.8;
  margin-bottom: 1rem;
  line-height: 1.5;
  font-size: 0.9rem;
}

.product-details {
  margin-bottom: 1.5rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.3rem;
  font-size: 0.9rem;
}

.detail-label {
  color: var(--text-dark);
  opacity: 0.7;
}

.detail-value {
  font-weight: 600;
  color: var(--text-dark);
}

.product-footer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.product-price {
  display: flex;
  align-items: baseline;
  gap: 0.3rem;
}

.price-amount {
  font-size: 1.5rem;
  font-weight: 800;
  color: var(--primary-color);
}

.price-unit {
  font-size: 0.9rem;
  color: var(--text-dark);
  opacity: 0.7;
}

.product-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.quantity-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #f8f9fa;
  border-radius: var(--border-radius);
  padding: 0.3rem;
}

.quantity-btn {
  width: 30px;
  height: 30px;
  border: none;
  background: white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  transition: var(--transition-fast);
}

.quantity-btn:hover:not(:disabled) {
  background: var(--primary-color);
  color: white;
}

.quantity-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity-display {
  min-width: 30px;
  text-align: center;
  font-weight: 600;
}

.add-to-cart-btn {
  flex: 1;
  padding: 0.8rem 1rem;
  font-size: 0.9rem;
  position: relative;
  overflow: hidden;
}

.add-to-cart-btn.adding {
  pointer-events: none;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  display: inline-block;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .product-actions {
    flex-direction: column;
    gap: 0.8rem;
  }
  
  .quantity-selector {
    align-self: center;
  }
  
  .add-to-cart-btn {
    width: 100%;
  }
}
</style>
