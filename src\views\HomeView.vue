<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import HeroSection from '@/components/HeroSection.vue'
import ProductCard from '@/components/ProductCard.vue'
import { useProductsStore } from '@/stores/products'
import type { Product } from '@/stores/cart'

gsap.registerPlugin(ScrollTrigger)

const productsStore = useProductsStore()
const featuredSectionRef = ref<HTMLElement>()
const aboutSectionRef = ref<HTMLElement>()

const quickViewProduct = ref<Product | null>(null)
const showQuickView = ref(false)

const handleQuickView = (product: Product) => {
  quickViewProduct.value = product
  showQuickView.value = true
}

const closeQuickView = () => {
  showQuickView.value = false
  quickViewProduct.value = null
}

const handleNewsletterSubmit = () => {
  // Newsletter submission logic would go here
  alert('Teşekkürler! E-posta listemize eklendi.')
}

onMounted(() => {
  // Scroll-triggered animations
  gsap.from('.featured-products .product-card', {
    scrollTrigger: {
      trigger: featuredSectionRef.value,
      start: 'top 80%',
      end: 'bottom 20%',
      toggleActions: 'play none none reverse'
    },
    duration: 0.8,
    y: 50,
    opacity: 0,
    stagger: 0.2,
    ease: 'power2.out'
  })

  gsap.from('.about-content > *', {
    scrollTrigger: {
      trigger: aboutSectionRef.value,
      start: 'top 80%',
      end: 'bottom 20%',
      toggleActions: 'play none none reverse'
    },
    duration: 0.8,
    y: 30,
    opacity: 0,
    stagger: 0.1,
    ease: 'power2.out'
  })
})
</script>

<template>
  <div class="home">
    <!-- Hero Section -->
    <HeroSection />

    <!-- Featured Products Section -->
    <section class="section featured-section" ref="featuredSectionRef">
      <div class="container">
        <h2 class="section-title">Öne Çıkan Ürünler</h2>
        <div class="featured-products grid grid-3">
          <ProductCard
            v-for="product in productsStore.featuredProducts"
            :key="product.id"
            :product="product"
            @quick-view="handleQuickView"
          />
        </div>
        <div class="section-cta">
          <RouterLink to="/products" class="btn btn-primary">
            Tüm Ürünleri Görüntüle
          </RouterLink>
        </div>
      </div>
    </section>

    <!-- About Section -->
    <section class="section about-section parallax" ref="aboutSectionRef">
      <div class="parallax-content">
        <div class="container">
          <div class="about-content">
            <h2 class="section-title">Neden Mutlu Elmalar?</h2>
            <div class="about-grid grid grid-2">
              <div class="about-text">
                <h3>Doğal ve Organik</h3>
                <p>
                  Elmalarımız tamamen doğal yöntemlerle, kimyasal gübre ve pestisit kullanılmadan yetiştirilir.
                  Sağlığınız için en iyisini sunuyoruz.
                </p>

                <h3>Taze ve Lezzetli</h3>
                <p>
                  Ağaçtan koparıldığı gün size ulaşan elmalarımız, doğal lezzetini ve besin değerini korur.
                  Her ısırıkta tazeliği hissedeceksiniz.
                </p>

                <h3>Güvenilir Kalite</h3>
                <p>
                  5 yıllık deneyimimizle, kalite standartlarımızdan asla ödün vermiyoruz.
                  Her elma özenle seçilir ve paketlenir.
                </p>
              </div>

              <div class="about-features">
                <div class="feature-item">
                  <div class="feature-icon">🌱</div>
                  <div class="feature-content">
                    <h4>%100 Organik</h4>
                    <p>Kimyasal madde içermez</p>
                  </div>
                </div>

                <div class="feature-item">
                  <div class="feature-icon">🚚</div>
                  <div class="feature-content">
                    <h4>Hızlı Teslimat</h4>
                    <p>24 saat içinde kapınızda</p>
                  </div>
                </div>

                <div class="feature-item">
                  <div class="feature-icon">💯</div>
                  <div class="feature-content">
                    <h4>Kalite Garantisi</h4>
                    <p>Memnun kalmazsan iade et</p>
                  </div>
                </div>

                <div class="feature-item">
                  <div class="feature-icon">🏆</div>
                  <div class="feature-content">
                    <h4>Ödüllü Lezzet</h4>
                    <p>Ulusal kalite ödülü sahibi</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Newsletter Section -->
    <section class="section newsletter-section">
      <div class="container">
        <div class="newsletter-content">
          <h2>Özel Fırsatlardan Haberdar Ol</h2>
          <p>Yeni ürünler ve indirimlerden ilk sen haberdar olmak için e-posta listemize katıl!</p>
          <form class="newsletter-form" @submit.prevent="handleNewsletterSubmit">
            <input
              type="email"
              placeholder="E-posta adresinizi girin"
              class="newsletter-input"
              required
            />
            <button type="submit" class="btn btn-primary">Abone Ol</button>
          </form>
        </div>
      </div>
    </section>

    <!-- Quick View Modal -->
    <Transition name="modal">
      <div v-if="showQuickView" class="modal-overlay" @click="closeQuickView">
        <div class="modal-content" @click.stop>
          <button class="modal-close" @click="closeQuickView">&times;</button>
          <div v-if="quickViewProduct" class="quick-view-content">
            <div class="quick-view-image">
              <img :src="quickViewProduct.image" :alt="quickViewProduct.name" />
            </div>
            <div class="quick-view-info">
              <h3>{{ quickViewProduct.name }}</h3>
              <p>{{ quickViewProduct.description }}</p>
              <div class="quick-view-details">
                <p><strong>Kategori:</strong> {{ quickViewProduct.category }}</p>
                <p><strong>Ağırlık:</strong> {{ quickViewProduct.weight }}</p>
                <p><strong>Menşei:</strong> {{ quickViewProduct.origin }}</p>
              </div>
              <div class="quick-view-price">
                {{ new Intl.NumberFormat('tr-TR', { style: 'currency', currency: 'TRY' }).format(quickViewProduct.price) }} / kg
              </div>
              <RouterLink :to="`/products`" class="btn btn-primary" @click="closeQuickView">
                Detayları Görüntüle
              </RouterLink>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </div>
</template>

<style scoped>
.home {
  overflow-x: hidden;
}

/* Featured Section */
.featured-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.section-cta {
  text-align: center;
  margin-top: 3rem;
}

/* About Section */
.about-section {
  background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(243, 156, 18, 0.1)),
              url('https://images.unsplash.com/photo-1542838132-92c53300491e?w=1920&h=1080&fit=crop') center/cover;
  color: var(--text-dark);
}

.about-section .parallax-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.about-grid {
  align-items: start;
  gap: 3rem;
}

.about-text h3 {
  color: var(--primary-color);
  margin-bottom: 1rem;
  margin-top: 2rem;
}

.about-text h3:first-child {
  margin-top: 0;
}

.about-text p {
  margin-bottom: 1.5rem;
  line-height: 1.7;
}

.about-features {
  display: grid;
  gap: 1.5rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border-radius: var(--border-radius);
  box-shadow: 0 4px 20px var(--shadow-light);
  transition: var(--transition-medium);
}

.feature-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px var(--shadow-medium);
}

.feature-icon {
  font-size: 2.5rem;
  flex-shrink: 0;
}

.feature-content h4 {
  margin-bottom: 0.3rem;
  color: var(--text-dark);
  font-size: 1.1rem;
}

.feature-content p {
  margin: 0;
  color: var(--text-dark);
  opacity: 0.8;
  font-size: 0.9rem;
}

/* Newsletter Section */
.newsletter-section {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
}

.newsletter-content {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.newsletter-content h2 {
  color: white;
  margin-bottom: 1rem;
}

.newsletter-content p {
  margin-bottom: 2rem;
  opacity: 0.9;
}

.newsletter-form {
  display: flex;
  gap: 1rem;
  max-width: 400px;
  margin: 0 auto;
}

.newsletter-input {
  flex: 1;
  padding: 1rem;
  border: none;
  border-radius: var(--border-radius);
  font-size: 1rem;
  outline: none;
}

.newsletter-input:focus {
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 2rem;
}

.modal-content {
  background: white;
  border-radius: var(--border-radius);
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

.modal-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  font-size: 2rem;
  cursor: pointer;
  color: var(--text-dark);
  z-index: 1;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-fast);
}

.modal-close:hover {
  background: var(--shadow-light);
}

.quick-view-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  padding: 2rem;
}

.quick-view-image img {
  width: 100%;
  height: 300px;
  object-fit: cover;
  border-radius: var(--border-radius);
}

.quick-view-info h3 {
  margin-bottom: 1rem;
  color: var(--text-dark);
}

.quick-view-details {
  margin: 1.5rem 0;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: var(--border-radius);
}

.quick-view-details p {
  margin-bottom: 0.5rem;
}

.quick-view-price {
  font-size: 1.5rem;
  font-weight: 800;
  color: var(--primary-color);
  margin: 1.5rem 0;
}

/* Modal Transitions */
.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .about-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .quick-view-content {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .newsletter-form {
    flex-direction: column;
  }

  .feature-item {
    padding: 1rem;
  }

  .feature-icon {
    font-size: 2rem;
  }

  .modal-overlay {
    padding: 1rem;
  }

  .quick-view-content {
    padding: 1.5rem;
  }
}
</style>
