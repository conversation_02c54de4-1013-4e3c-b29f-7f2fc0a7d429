/*!
 * GSDevTools 3.13.0
 * https://gsap.com
 * 
 * @license Copyright 2025, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license.
 * @author: <PERSON>, <EMAIL>
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).window=e.window||{})}(this,function(e){"use strict";function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function w(e,t){if(e.parentNode&&(g||M(e))){var o=P(e),n=o?o.getAttribute("xmlns")||"http://www.w3.org/2000/svg":"http://www.w3.org/1999/xhtml",i=o?t?"rect":"g":"div",r=2!==t?0:100,a=3===t?100:0,s="position:absolute;display:block;pointer-events:none;margin:0;padding:0;",l=g.createElementNS?g.createElementNS(n.replace(/^https/,"http"),i):g.createElement(i);return t&&(o?(m=m||w(e),l.setAttribute("width",.01),l.setAttribute("height",.01),l.setAttribute("transform","translate("+r+","+a+")"),m.appendChild(l)):(h||((h=w(e)).style.cssText=s),l.style.cssText=s+"width:0.1px;height:0.1px;top:"+a+"px;left:"+r+"px",h.appendChild(l))),l}throw"Need document and parent."}function A(e,t,o,n,i,r,a){return e.a=t,e.b=o,e.c=n,e.d=i,e.e=r,e.f=a,e}var g,f,r,a,h,m,v,x,y,t,b="transform",T=b+"Origin",M=function _setDoc(e){var t=e.ownerDocument||e;!(b in e.style)&&"msTransform"in e.style&&(T=(b="msTransform")+"Origin");for(;t.parentNode&&(t=t.parentNode););if(f=window,v=new he,t){r=(g=t).documentElement,a=t.body,(x=g.createElementNS("http://www.w3.org/2000/svg","g")).style.transform="none";var o=t.createElement("div"),n=t.createElement("div"),i=t&&(t.body||t.firstElementChild);i&&i.appendChild&&(i.appendChild(o),o.appendChild(n),o.setAttribute("style","position:static;transform:translate3d(0,0,1px)"),y=n.offsetParent!==o,i.removeChild(o))}return t},D=function _forceNonZeroScale(e){for(var t,o;e&&e!==a;)(o=e._gsap)&&o.uncache&&o.get(e,"x"),o&&!o.scaleX&&!o.scaleY&&o.renderTransform&&(o.scaleX=o.scaleY=1e-4,o.renderTransform(1,o),t?t.push(o):t=[o]),e=e.parentNode;return t},k=[],S=[],C=function _getDocScrollTop(){return f.pageYOffset||g.scrollTop||r.scrollTop||a.scrollTop||0},E=function _getDocScrollLeft(){return f.pageXOffset||g.scrollLeft||r.scrollLeft||a.scrollLeft||0},P=function _svgOwner(e){return e.ownerSVGElement||("svg"===(e.tagName+"").toLowerCase()?e:null)},L=function _isFixed(e){return"fixed"===f.getComputedStyle(e).position||((e=e.parentNode)&&1===e.nodeType?_isFixed(e):void 0)},N=function _placeSiblings(e,t){var o,n,i,r,a,s,l=P(e),c=e===l,d=l?k:S,p=e.parentNode,u=p&&!l&&p.shadowRoot&&p.shadowRoot.appendChild?p.shadowRoot:p;if(e===f)return e;if(d.length||d.push(w(e,1),w(e,2),w(e,3)),o=l?m:h,l)c?(r=-(i=function _getCTM(e){var t,o=e.getCTM();return o||(t=e.style[b],e.style[b]="none",e.appendChild(x),o=x.getCTM(),e.removeChild(x),t?e.style[b]=t:e.style.removeProperty(b.replace(/([A-Z])/g,"-$1").toLowerCase())),o||v.clone()}(e)).e/i.a,a=-i.f/i.d,n=v):e.getBBox?(i=e.getBBox(),r=(n=(n=e.transform?e.transform.baseVal:{}).numberOfItems?1<n.numberOfItems?function _consolidate(e){for(var t=new he,o=0;o<e.numberOfItems;o++)t.multiply(e.getItem(o).matrix);return t}(n):n.getItem(0).matrix:v).a*i.x+n.c*i.y,a=n.b*i.x+n.d*i.y):(n=new he,r=a=0),t&&"g"===e.tagName.toLowerCase()&&(r=a=0),(c?l:p).appendChild(o),o.setAttribute("transform","matrix("+n.a+","+n.b+","+n.c+","+n.d+","+(n.e+r)+","+(n.f+a)+")");else{if(r=a=0,y)for(n=e.offsetParent,i=e;(i=i&&i.parentNode)&&i!==n&&i.parentNode;)4<(f.getComputedStyle(i)[b]+"").length&&(r=i.offsetLeft,a=i.offsetTop,i=0);if("absolute"!==(s=f.getComputedStyle(e)).position&&"fixed"!==s.position)for(n=e.offsetParent;p&&p!==n;)r+=p.scrollLeft||0,a+=p.scrollTop||0,p=p.parentNode;(i=o.style).top=e.offsetTop-a+"px",i.left=e.offsetLeft-r+"px",i[b]=s[b],i[T]=s[T],i.position="fixed"===s.position?"fixed":"absolute",u.appendChild(o)}return o},he=((t=Matrix2D.prototype).inverse=function inverse(){var e=this.a,t=this.b,o=this.c,n=this.d,i=this.e,r=this.f,a=e*n-t*o||1e-10;return A(this,n/a,-t/a,-o/a,e/a,(o*r-n*i)/a,-(e*r-t*i)/a)},t.multiply=function multiply(e){var t=this.a,o=this.b,n=this.c,i=this.d,r=this.e,a=this.f,s=e.a,l=e.c,c=e.b,d=e.d,p=e.e,u=e.f;return A(this,s*t+c*n,s*o+c*i,l*t+d*n,l*o+d*i,r+p*t+u*n,a+p*o+u*i)},t.clone=function clone(){return new Matrix2D(this.a,this.b,this.c,this.d,this.e,this.f)},t.equals=function equals(e){var t=this.a,o=this.b,n=this.c,i=this.d,r=this.e,a=this.f;return t===e.a&&o===e.b&&n===e.c&&i===e.d&&r===e.e&&a===e.f},t.apply=function apply(e,t){void 0===t&&(t={});var o=e.x,n=e.y,i=this.a,r=this.b,a=this.c,s=this.d,l=this.e,c=this.f;return t.x=o*i+n*a+l||0,t.y=o*r+n*s+c||0,t},Matrix2D);function Matrix2D(e,t,o,n,i,r){void 0===e&&(e=1),void 0===t&&(t=0),void 0===o&&(o=0),void 0===n&&(n=1),void 0===i&&(i=0),void 0===r&&(r=0),A(this,e,t,o,n,i,r)}function getGlobalMatrix(e,t,o,n){if(!e||!e.parentNode||(g||M(e)).documentElement===e)return new he;var i=D(e),r=P(e)?k:S,a=N(e,o),s=r[0].getBoundingClientRect(),l=r[1].getBoundingClientRect(),c=r[2].getBoundingClientRect(),d=a.parentNode,p=!n&&L(e),u=new he((l.left-s.left)/100,(l.top-s.top)/100,(c.left-s.left)/100,(c.top-s.top)/100,s.left+(p?0:E()),s.top+(p?0:C()));if(d.removeChild(a),i)for(s=i.length;s--;)(l=i[s]).scaleX=l.scaleY=0,l.renderTransform(1,l);return t?u.inverse():u}function X(){return"undefined"!=typeof window}function Y(){return me||X()&&(me=window.gsap)&&me.registerPlugin&&me}function Z(e){return"function"==typeof e}function $(e){return"object"==typeof e}function _(e){return void 0===e}function aa(){return!1}function da(e){return Math.round(1e4*e)/1e4}function fa(e,t){var o=xe.createElementNS?xe.createElementNS((t||"http://www.w3.org/1999/xhtml").replace(/^https/,"http"),e):xe.createElement(e);return o.style?o:xe.createElement(e)}function ra(e,t){var o,n={};for(o in e)n[o]=t?e[o]*t:e[o];return n}function ta(e,t){for(var o,n=e.length;n--;)t?e[n].style.touchAction=t:e[n].style.removeProperty("touch-action"),(o=e[n].children)&&o.length&&ta(o,t)}function ua(){return He.forEach(function(e){return e()})}function wa(){return!He.length&&me.ticker.remove(ua)}function xa(e){for(var t=He.length;t--;)He[t]===e&&He.splice(t,1);me.to(wa,{overwrite:!0,delay:15,duration:0,onComplete:wa,data:"_draggable"})}function za(e,t,o,n){if(e.addEventListener){var i=Me[t];n=n||(d?{passive:!1}:null),e.addEventListener(i||t,o,n),i&&t!==i&&e.addEventListener(t,o,n)}}function Aa(e,t,o,n){if(e.removeEventListener){var i=Me[t];e.removeEventListener(i||t,o,n),i&&t!==i&&e.removeEventListener(t,o,n)}}function Ba(e){e.preventDefault&&e.preventDefault(),e.preventManipulation&&e.preventManipulation()}function Da(e){De=e.touches&&Ne<e.touches.length,Aa(e.target,"touchend",Da)}function Ea(e){De=e.touches&&Ne<e.touches.length,za(e.target,"touchend",Da)}function Fa(e){return ve.pageYOffset||e.scrollTop||e.documentElement.scrollTop||e.body.scrollTop||0}function Ga(e){return ve.pageXOffset||e.scrollLeft||e.documentElement.scrollLeft||e.body.scrollLeft||0}function Ha(e,t){za(e,"scroll",t),Ze(e.parentNode)||Ha(e.parentNode,t)}function Ia(e,t){Aa(e,"scroll",t),Ze(e.parentNode)||Ia(e.parentNode,t)}function Ka(e,t){var o="x"===t?"Width":"Height",n="scroll"+o,i="client"+o;return Math.max(0,Ze(e)?Math.max(ye[n],s[n])-(ve["inner"+o]||ye[i]||s[i]):e[n]-e[i])}function La(e,t){var o=Ka(e,"x"),n=Ka(e,"y");Ze(e)?e=Ge:La(e.parentNode,t),e._gsMaxScrollX=o,e._gsMaxScrollY=n,t||(e._gsScrollX=e.scrollLeft||0,e._gsScrollY=e.scrollTop||0)}function Ma(e,t,o){var n=e.style;n&&(_(n[t])&&(t=c(t,e)||t),null==o?n.removeProperty&&n.removeProperty(t.replace(/([A-Z])/g,"-$1").toLowerCase()):n[t]=o)}function Na(e){return ve.getComputedStyle(e instanceof Element?e:e.host||(e.parentNode||{}).host||e)}function Pa(e){if(e===ve)return p.left=p.top=0,p.width=p.right=ye.clientWidth||e.innerWidth||s.clientWidth||0,p.height=p.bottom=(e.innerHeight||0)-20<ye.clientHeight?ye.clientHeight:e.innerHeight||s.clientHeight||0,p;var t=e.ownerDocument||xe,o=_(e.pageX)?e.nodeType||_(e.left)||_(e.top)?_e(e)[0].getBoundingClientRect():e:{left:e.pageX-Ga(t),top:e.pageY-Fa(t),right:e.pageX-Ga(t)+1,bottom:e.pageY-Fa(t)+1};return _(o.right)&&!_(o.width)?(o.right=o.left+o.width,o.bottom=o.top+o.height):_(o.width)&&(o={width:o.right-o.left,height:o.bottom-o.top,right:o.right,left:o.left,bottom:o.bottom,top:o.top}),o}function Qa(e,t,o){var n,i=e.vars,r=i[o],a=e._listeners[t];return Z(r)&&(n=r.apply(i.callbackScope||e,i[o+"Params"]||[e.pointerEvent])),a&&!1===e.dispatchEvent(t)&&(n=!1),n}function Ra(e,t){var o,n,i,r=_e(e)[0];return r.nodeType||r===ve?u(r,t):_(e.left)?{left:n=e.min||e.minX||e.minRotation||0,top:o=e.min||e.minY||0,width:(e.max||e.maxX||e.maxRotation||0)-n,height:(e.max||e.maxY||0)-o}:(i={x:0,y:0},{left:e.left-i.x,top:e.top-i.y,width:e.width,height:e.height})}function Ua(i,r,e,t,a,o){var n,s,l,c={};if(r)if(1!==a&&r instanceof Array){if(c.end=n=[],l=r.length,$(r[0]))for(s=0;s<l;s++)n[s]=ra(r[s],a);else for(s=0;s<l;s++)n[s]=r[s]*a;e+=1.1,t-=1.1}else Z(r)?c.end=function(e){var t,o,n=r.call(i,e);if(1!==a)if($(n)){for(o in t={},n)t[o]=n[o]*a;n=t}else n*=a;return n}:c.end=r;return!e&&0!==e||(c.max=e),!t&&0!==t||(c.min=t),o&&(c.velocity=0),c}function Va(e){var t;return!(!e||!e.getAttribute||e===s)&&(!("true"!==(t=e.getAttribute("data-clickable"))&&("false"===t||!n.test(e.nodeName+"")&&"true"!==e.getAttribute("contentEditable")))||Va(e.parentNode))}function Wa(e,t){for(var o,n=e.length;n--;)(o=e[n]).ondragstart=o.onselectstart=t?null:aa,me.set(o,{lazy:!0,userSelect:t?"text":"none"})}function $a(r,i){r=me.utils.toArray(r)[0],i=i||{};var a,s,l,e,c,d,p=document.createElement("div"),u=p.style,t=r.firstChild,g=0,f=0,h=r.scrollTop,m=r.scrollLeft,v=r.scrollWidth,x=r.scrollHeight,y=0,b=0,w=0;R&&!1!==i.force3D?(c="translate3d(",d="px,0px)"):H&&(c="translate(",d="px)"),this.scrollTop=function(e,t){if(!arguments.length)return-this.top();this.top(-e,t)},this.scrollLeft=function(e,t){if(!arguments.length)return-this.left();this.left(-e,t)},this.left=function(e,t){if(!arguments.length)return-(r.scrollLeft+f);var o=r.scrollLeft-m,n=f;if((2<o||o<-2)&&!t)return m=r.scrollLeft,me.killTweensOf(this,{left:1,scrollLeft:1}),this.left(-m),void(i.onKill&&i.onKill());(e=-e)<0?(f=e-.5|0,e=0):b<e?(f=e-b|0,e=b):f=0,(f||n)&&(this._skip||(u[H]=c+-f+"px,"+-g+d),0<=f+y&&(u.paddingRight=f+y+"px")),r.scrollLeft=0|e,m=r.scrollLeft},this.top=function(e,t){if(!arguments.length)return-(r.scrollTop+g);var o=r.scrollTop-h,n=g;if((2<o||o<-2)&&!t)return h=r.scrollTop,me.killTweensOf(this,{top:1,scrollTop:1}),this.top(-h),void(i.onKill&&i.onKill());(e=-e)<0?(g=e-.5|0,e=0):w<e?(g=e-w|0,e=w):g=0,(g||n)&&(this._skip||(u[H]=c+-f+"px,"+-g+d)),r.scrollTop=0|e,h=r.scrollTop},this.maxScrollTop=function(){return w},this.maxScrollLeft=function(){return b},this.disable=function(){for(t=p.firstChild;t;)e=t.nextSibling,r.appendChild(t),t=e;r===p.parentNode&&r.removeChild(p)},this.enable=function(){if((t=r.firstChild)!==p){for(;t;)e=t.nextSibling,p.appendChild(t),t=e;r.appendChild(p),this.calibrate()}},this.calibrate=function(e){var t,o,n,i=r.clientWidth===a;h=r.scrollTop,m=r.scrollLeft,i&&r.clientHeight===s&&p.offsetHeight===l&&v===r.scrollWidth&&x===r.scrollHeight&&!e||((g||f)&&(o=this.left(),n=this.top(),this.left(-r.scrollLeft),this.top(-r.scrollTop)),t=Na(r),i&&!e||(u.display="block",u.width="auto",u.paddingRight="0px",(y=Math.max(0,r.scrollWidth-r.clientWidth))&&(y+=parseFloat(t.paddingLeft)+(B?parseFloat(t.paddingRight):0))),u.display="inline-block",u.position="relative",u.overflow="visible",u.verticalAlign="top",u.boxSizing="content-box",u.width="100%",u.paddingRight=y+"px",B&&(u.paddingBottom=t.paddingBottom),a=r.clientWidth,s=r.clientHeight,v=r.scrollWidth,x=r.scrollHeight,b=r.scrollWidth-a,w=r.scrollHeight-s,l=p.offsetHeight,u.display="block",(o||n)&&(this.left(o),this.top(n)))},this.content=p,this.element=r,this._skip=!1,this.enable()}function _a(e){if(X()&&document.body){var t=window&&window.navigator;ve=window,xe=document,ye=xe.documentElement,s=xe.body,l=fa("div"),Ee=!!window.PointerEvent,(be=fa("div")).style.cssText="visibility:hidden;height:1px;top:-1px;pointer-events:none;position:relative;clear:both;cursor:grab",Ce="grab"===be.style.cursor?"grab":"move",ke=t&&-1!==t.userAgent.toLowerCase().indexOf("android"),Te="ontouchstart"in ye&&"orientation"in ve||t&&(0<t.MaxTouchPoints||0<t.msMaxTouchPoints),n=fa("div"),i=fa("div"),r=i.style,a=s,r.display="inline-block",r.position="relative",n.style.cssText="width:90px;height:40px;padding:10px;overflow:auto;visibility:hidden",n.appendChild(i),a.appendChild(n),o=i.offsetHeight+18>n.scrollHeight,a.removeChild(n),B=o,Me=function(e){for(var t=e.split(","),o=(("onpointerdown"in l?"pointerdown,pointermove,pointerup,pointercancel":"onmspointerdown"in l?"MSPointerDown,MSPointerMove,MSPointerUp,MSPointerCancel":e).split(",")),n={},i=4;-1<--i;)n[t[i]]=o[i],n[o[i]]=t[i];try{ye.addEventListener("test",null,Object.defineProperty({},"passive",{get:function get(){d=1}}))}catch(e){}return n}("touchstart,touchmove,touchend,touchcancel"),za(xe,"touchcancel",aa),za(ve,"touchmove",aa),s&&s.addEventListener("touchstart",aa),za(xe,"contextmenu",function(){for(var e in Oe)Oe[e].isPressed&&Oe[e].endDrag()}),me=we=Y()}var o,n,i,r,a;me?(Se=me.plugins.inertia,Pe=me.core.context||function(){},c=me.utils.checkPrefix,H=c(H),Re=c(Re),_e=me.utils.toArray,Le=me.core.getStyleSaver,R=!!c("perspective")):e&&console.warn("Please gsap.registerPlugin(Draggable)")}var me,ve,xe,ye,s,l,be,we,c,_e,d,Te,Me,De,ke,Se,Ce,Ee,Pe,Le,R,B,o,Ne=0,H="transform",Re="transformOrigin",Ae=Array.isArray,Xe=180/Math.PI,Be=1e20,i=new he,Ye=Date.now||function(){return(new Date).getTime()},He=[],Oe={},Ie=0,n=/^(?:a|input|textarea|button|select)$/i,Fe=0,ze={},Ge={},Ze=function _isRoot(e){return!(e&&e!==ye&&9!==e.nodeType&&e!==xe.body&&e!==ve&&e.nodeType&&e.parentNode)},p={},We={},u=function _getElementBounds(e,t){t=_e(t)[0];var o,n,i,r,a,s,l,c,d,p,u,g,f,h=e.getBBox&&e.ownerSVGElement,m=e.ownerDocument||xe;if(e===ve)i=Fa(m),n=(o=Ga(m))+(m.documentElement.clientWidth||e.innerWidth||m.body.clientWidth||0),r=i+((e.innerHeight||0)-20<m.documentElement.clientHeight?m.documentElement.clientHeight:e.innerHeight||m.body.clientHeight||0);else{if(t===ve||_(t))return e.getBoundingClientRect();o=i=0,h?(u=(p=e.getBBox()).width,g=p.height):(e.viewBox&&(p=e.viewBox.baseVal)&&(o=p.x||0,i=p.y||0,u=p.width,g=p.height),u||(p="border-box"===(f=Na(e)).boxSizing,u=(parseFloat(f.width)||e.clientWidth||0)+(p?0:parseFloat(f.borderLeftWidth)+parseFloat(f.borderRightWidth)),g=(parseFloat(f.height)||e.clientHeight||0)+(p?0:parseFloat(f.borderTopWidth)+parseFloat(f.borderBottomWidth)))),n=u,r=g}return e===t?{left:o,top:i,width:n-o,height:r-i}:(s=(a=getGlobalMatrix(t,!0).multiply(getGlobalMatrix(e))).apply({x:o,y:i}),l=a.apply({x:n,y:i}),c=a.apply({x:n,y:r}),d=a.apply({x:o,y:r}),{left:o=Math.min(s.x,l.x,c.x,d.x),top:i=Math.min(s.y,l.y,c.y,d.y),width:Math.max(s.x,l.x,c.x,d.x)-o,height:Math.max(s.y,l.y,c.y,d.y)-i})},O=((o=EventDispatcher.prototype).addEventListener=function addEventListener(e,t){var o=this._listeners[e]||(this._listeners[e]=[]);~o.indexOf(t)||o.push(t)},o.removeEventListener=function removeEventListener(e,t){var o=this._listeners[e],n=o&&o.indexOf(t);0<=n&&o.splice(n,1)},o.dispatchEvent=function dispatchEvent(t){var o,n=this;return(this._listeners[t]||[]).forEach(function(e){return!1===e.call(n,{type:t,target:n.target})&&(o=!1)}),o},EventDispatcher);function EventDispatcher(e){this._listeners={},this.target=e||this}var Ve,W=(function _inheritsLoose(e,t){e.prototype=Object.create(t.prototype),(e.prototype.constructor=e).__proto__=t}(Draggable,Ve=O),Draggable.register=function register(e){me=e,_a()},Draggable.create=function create(e,t){return we||_a(!0),_e(e).map(function(e){return new Draggable(e,t)})},Draggable.get=function get(e){return Oe[(_e(e)[0]||{})._gsDragID]},Draggable.timeSinceDrag=function timeSinceDrag(){return(Ye()-Fe)/1e3},Draggable.hitTest=function hitTest(e,t,o){if(e===t)return!1;var n,i,r,a=Pa(e),s=Pa(t),l=a.top,c=a.left,d=a.right,p=a.bottom,u=a.width,g=a.height,f=s.left>d||s.right<c||s.top>p||s.bottom<l;return f||!o?!f:(r=-1!==(o+"").indexOf("%"),o=parseFloat(o)||0,(n={left:Math.max(c,s.left),top:Math.max(l,s.top)}).width=Math.min(d,s.right)-n.left,n.height=Math.min(p,s.bottom)-n.top,!(n.width<0||n.height<0)&&(r?u*g*(o*=.01)<=(i=n.width*n.height)||i>=s.width*s.height*o:n.width>o&&n.height>o))},Draggable);function Draggable(g,p){var e;e=Ve.call(this)||this,we||_a(1),g=_e(g)[0],e.styles=Le&&Le(g,"transform,left,top"),Se=Se||me.plugins.inertia,e.vars=p=ra(p||{}),e.target=g,e.x=e.y=e.rotation=0,e.dragResistance=parseFloat(p.dragResistance)||0,e.edgeResistance=isNaN(p.edgeResistance)?1:parseFloat(p.edgeResistance)||0,e.lockAxis=p.lockAxis,e.autoScroll=p.autoScroll||0,e.lockedAxis=null,e.allowEventDefault=!!p.allowEventDefault,me.getProperty(g,"x");function Mh(e,t){return parseFloat(le.get(g,e,t))}function ti(e){return Ba(e),e.stopImmediatePropagation&&e.stopImmediatePropagation(),!1}function ui(e){if(j.autoScroll&&j.isDragging&&(te||L)){var t,o,n,i,r,a,s,l,c=g,d=15*j.autoScroll;for(te=!1,Ge.scrollTop=null!=ve.pageYOffset?ve.pageYOffset:null!=de.documentElement.scrollTop?de.documentElement.scrollTop:de.body.scrollTop,Ge.scrollLeft=null!=ve.pageXOffset?ve.pageXOffset:null!=de.documentElement.scrollLeft?de.documentElement.scrollLeft:de.body.scrollLeft,i=j.pointerX-Ge.scrollLeft,r=j.pointerY-Ge.scrollTop;c&&!o;)t=(o=Ze(c.parentNode))?Ge:c.parentNode,n=o?{bottom:Math.max(ye.clientHeight,ve.innerHeight||0),right:Math.max(ye.clientWidth,ve.innerWidth||0),left:0,top:0}:t.getBoundingClientRect(),a=s=0,K&&((l=t._gsMaxScrollY-t.scrollTop)<0?s=l:r>n.bottom-ie&&l?(te=!0,s=Math.min(l,d*(1-Math.max(0,n.bottom-r)/ie)|0)):r<n.top+oe&&t.scrollTop&&(te=!0,s=-Math.min(t.scrollTop,d*(1-Math.max(0,r-n.top)/oe)|0)),s&&(t.scrollTop+=s)),U&&((l=t._gsMaxScrollX-t.scrollLeft)<0?a=l:i>n.right-ne&&l?(te=!0,a=Math.min(l,d*(1-Math.max(0,n.right-i)/ne)|0)):i<n.left+re&&t.scrollLeft&&(te=!0,a=-Math.min(t.scrollLeft,d*(1-Math.max(0,i-n.left)/re)|0)),a&&(t.scrollLeft+=a)),o&&(a||s)&&(ve.scrollTo(t.scrollLeft,t.scrollTop),ge(j.pointerX+a,j.pointerY+s)),c=t}if(L){var p=j.x,u=j.y;W?(j.deltaX=p-parseFloat(le.rotation),j.rotation=p,le.rotation=p+"deg",le.renderTransform(1,le)):f?(K&&(j.deltaY=u-f.top(),f.top(u)),U&&(j.deltaX=p-f.left(),f.left(p))):G?(K&&(j.deltaY=u-parseFloat(le.y),le.y=u+"px"),U&&(j.deltaX=p-parseFloat(le.x),le.x=p+"px"),le.renderTransform(1,le)):(K&&(j.deltaY=u-parseFloat(g.style.top||0),g.style.top=u+"px"),U&&(j.deltaX=p-parseFloat(g.style.left||0),g.style.left=p+"px")),!h||e||O||(!(O=!0)===Qa(j,"drag","onDrag")&&(U&&(j.x-=j.deltaX),K&&(j.y-=j.deltaY),ui(!0)),O=!1)}L=!1}function vi(e,t){var o,n,i=j.x,r=j.y;g._gsap||(le=me.core.getCache(g)),le.uncache&&me.getProperty(g,"x"),G?(j.x=parseFloat(le.x),j.y=parseFloat(le.y)):W?j.x=j.rotation=parseFloat(le.rotation):f?(j.y=f.top(),j.x=f.left()):(j.y=parseFloat(g.style.top||(n=Na(g))&&n.top)||0,j.x=parseFloat(g.style.left||(n||{}).left)||0),(N||R||A)&&!t&&(j.isDragging||j.isThrowing)&&(A&&(ze.x=j.x,ze.y=j.y,(o=A(ze)).x!==j.x&&(j.x=o.x,L=!0),o.y!==j.y&&(j.y=o.y,L=!0)),N&&(o=N(j.x))!==j.x&&(j.x=o,W&&(j.rotation=o),L=!0),R&&((o=R(j.y))!==j.y&&(j.y=o),L=!0)),L&&ui(!0),e||(j.deltaX=j.x-i,j.deltaY=j.y-r,Qa(j,"throwupdate","onThrowUpdate"))}function wi(a,s,l,o){return null==s&&(s=-Be),null==l&&(l=Be),Z(a)?function(e){var t=j.isPressed?1-j.edgeResistance:1;return a.call(j,(l<e?l+(e-l)*t:e<s?s+(e-s)*t:e)*o)*o}:Ae(a)?function(e){for(var t,o,n=a.length,i=0,r=Be;-1<--n;)(o=(t=a[n])-e)<0&&(o=-o),o<r&&s<=t&&t<=l&&(i=n,r=o);return a[i]}:isNaN(a)?function(e){return e}:function(){return a*o}}function yi(){var e,t,o,n;D=!1,f?(f.calibrate(),j.minX=S=-f.maxScrollLeft(),j.minY=E=-f.maxScrollTop(),j.maxX=k=j.maxY=C=0,D=!0):p.bounds&&(e=Ra(p.bounds,g.parentNode),W?(j.minX=S=e.left,j.maxX=k=e.left+e.width,j.minY=E=j.maxY=C=0):_(p.bounds.maxX)&&_(p.bounds.maxY)?(t=Ra(g,g.parentNode),j.minX=S=Math.round(Mh(V,"px")+e.left-t.left),j.minY=E=Math.round(Mh(Q,"px")+e.top-t.top),j.maxX=k=Math.round(S+(e.width-t.width)),j.maxY=C=Math.round(E+(e.height-t.height))):(e=p.bounds,j.minX=S=e.minX,j.minY=E=e.minY,j.maxX=k=e.maxX,j.maxY=C=e.maxY),k<S&&(j.minX=k,j.maxX=k=S,S=j.minX),C<E&&(j.minY=C,j.maxY=C=E,E=j.minY),W&&(j.minRotation=S,j.maxRotation=k),D=!0),p.liveSnap&&(o=!0===p.liveSnap?p.snap||{}:p.liveSnap,n=Ae(o)||Z(o),W?(N=wi(n?o:o.rotation,S,k,1),R=null):o.points?A=function buildPointSnapFunc(l,s,c,d,p,u,g){return u=u&&u<Be?u*u:Be,Z(l)?function(e){var t,o,n,i=j.isPressed?1-j.edgeResistance:1,r=e.x,a=e.y;return e.x=r=c<r?c+(r-c)*i:r<s?s+(r-s)*i:r,e.y=a=p<a?p+(a-p)*i:a<d?d+(a-d)*i:a,(t=l.call(j,e))!==e&&(e.x=t.x,e.y=t.y),1!==g&&(e.x*=g,e.y*=g),u<Be&&(o=e.x-r,n=e.y-a,u<o*o+n*n&&(e.x=r,e.y=a)),e}:Ae(l)?function(e){for(var t,o,n,i,r=l.length,a=0,s=Be;-1<--r;)(i=(t=(n=l[r]).x-e.x)*t+(o=n.y-e.y)*o)<s&&(a=r,s=i);return s<=u?l[a]:e}:function(e){return e}}(n?o:o.points,S,k,E,C,o.radius,f?-1:1):(U&&(N=wi(n?o:o.x||o.left||o.scrollLeft,S,k,f?-1:1)),K&&(R=wi(n?o:o.y||o.top||o.scrollTop,E,C,f?-1:1))))}function zi(){j.isThrowing=!1,Qa(j,"throwcomplete","onThrowComplete")}function Ai(){j.isThrowing=!1}function Bi(e,t){var o,n,i,r;e&&Se?(!0===e&&(o=p.snap||p.liveSnap||{},n=Ae(o)||Z(o),e={resistance:(p.throwResistance||p.resistance||1e3)/(W?10:1)},W?e.rotation=Ua(j,n?o:o.rotation,k,S,1,t):(U&&(e[V]=Ua(j,n?o:o.points||o.x||o.left,k,S,f?-1:1,t||"x"===j.lockedAxis)),K&&(e[Q]=Ua(j,n?o:o.points||o.y||o.top,C,E,f?-1:1,t||"y"===j.lockedAxis)),(o.points||Ae(o)&&$(o[0]))&&(e.linkedProps=V+","+Q,e.radius=o.radius))),j.isThrowing=!0,r=isNaN(p.overshootTolerance)?1===p.edgeResistance?0:1-j.edgeResistance+.2:p.overshootTolerance,e.duration||(e.duration={max:Math.max(p.minDuration||0,"maxDuration"in p?p.maxDuration:2),min:isNaN(p.minDuration)?0===r||$(e)&&1e3<e.resistance?0:.5:p.minDuration,overshoot:r}),j.tween=i=me.to(f||g,{inertia:e,data:"_draggable",inherit:!1,onComplete:zi,onInterrupt:Ai,onUpdate:p.fastMode?Qa:vi,onUpdateParams:p.fastMode?[j,"onthrowupdate","onThrowUpdate"]:o&&o.radius?[!1,!0]:[]}),p.fastMode||(f&&(f._skip=!0),i.render(1e9,!0,!0),vi(!0,!0),j.endX=j.x,j.endY=j.y,W&&(j.endRotation=j.x),i.play(0),vi(!0,!0),f&&(f._skip=!1))):D&&j.applyBounds()}function Ci(e){var t,o=X;X=getGlobalMatrix(g.parentNode,!0),e&&j.isPressed&&!X.equals(o||new he)&&(t=o.inverse().apply({x:b,y:w}),X.apply(t,t),b=t.x,w=t.y),X.equals(i)&&(X=null)}function Di(){var e,t,o,n=1-j.edgeResistance,i=ce?Ga(de):0,r=ce?Fa(de):0;G&&(le.x=Mh(V,"px")+"px",le.y=Mh(Q,"px")+"px",le.renderTransform()),Ci(!1),We.x=j.pointerX-i,We.y=j.pointerY-r,X&&X.apply(We,We),b=We.x,w=We.y,L&&(ge(j.pointerX,j.pointerY),ui(!0)),d=getGlobalMatrix(g),f?(yi(),M=f.top(),T=f.left()):(pe()?(vi(!0,!0),yi()):j.applyBounds(),W?(e=g.ownerSVGElement?[le.xOrigin-g.getBBox().x,le.yOrigin-g.getBBox().y]:(Na(g)[Re]||"0 0").split(" "),P=j.rotationOrigin=getGlobalMatrix(g).apply({x:parseFloat(e[0])||0,y:parseFloat(e[1])||0}),vi(!0,!0),t=j.pointerX-P.x-i,o=P.y-j.pointerY+r,T=j.x,M=j.y=Math.atan2(o,t)*Xe):(M=Mh(Q,"px"),T=Mh(V,"px"))),D&&n&&(k<T?T=k+(T-k)/n:T<S&&(T=S-(S-T)/n),W||(C<M?M=C+(M-C)/n:M<E&&(M=E-(E-M)/n))),j.startX=T=da(T),j.startY=M=da(M)}function Fi(){!be.parentNode||pe()||j.isDragging||be.parentNode.removeChild(be)}function Gi(e,t){var o;if(!u||j.isPressed||!e||!("mousedown"!==e.type&&"pointerdown"!==e.type||t)&&Ye()-se<30&&Me[j.pointerEvent.type])F&&e&&u&&Ba(e);else{if(B=pe(),z=!1,j.pointerEvent=e,Me[e.type]?(y=~e.type.indexOf("touch")?e.currentTarget||e.target:de,za(y,"touchend",fe),za(y,"touchmove",ue),za(y,"touchcancel",fe),za(de,"touchstart",Ea)):(y=null,za(de,"mousemove",ue)),H=null,Ee&&y||(za(de,"mouseup",fe),e&&e.target&&za(e.target,"mouseup",fe)),x=ae.call(j,e.target)&&!1===p.dragClickables&&!t)return za(e.target,"change",fe),Qa(j,"pressInit","onPressInit"),Qa(j,"press","onPress"),Wa(J,!0),void(F=!1);if(Y=!(!y||U==K||!1===j.vars.allowNativeTouchScrolling||j.vars.allowContextMenu&&e&&(e.ctrlKey||2<e.which))&&(U?"y":"x"),(F=!Y&&!j.allowEventDefault)&&(Ba(e),za(ve,"touchforcechange",Ba)),e.changedTouches?(e=m=e.changedTouches[0],v=e.identifier):e.pointerId?v=e.pointerId:m=v=null,Ne++,function _addToRenderQueue(e){He.push(e),1===He.length&&me.ticker.add(ua)}(ui),w=j.pointerY=e.pageY,b=j.pointerX=e.pageX,Qa(j,"pressInit","onPressInit"),(Y||j.autoScroll)&&La(g.parentNode),!g.parentNode||!j.autoScroll||f||W||!g.parentNode._gsMaxScrollX||be.parentNode||g.getBBox||(be.style.width=g.parentNode.scrollWidth+"px",g.parentNode.appendChild(be)),Di(),j.tween&&j.tween.kill(),j.isThrowing=!1,me.killTweensOf(f||g,n,!0),f&&me.killTweensOf(g,{scrollTo:1},!0),j.tween=j.lockedAxis=null,!p.zIndexBoost&&(W||f||!1===p.zIndexBoost)||(g.style.zIndex=Draggable.zIndex++),j.isPressed=!0,h=!(!p.onDrag&&!j._listeners.drag),l=!(!p.onMove&&!j._listeners.move),!1!==p.cursor||p.activeCursor)for(o=J.length;-1<--o;)me.set(J[o],{cursor:p.activeCursor||p.cursor||("grab"===Ce?"grabbing":Ce)});Qa(j,"press","onPress")}}function Ki(e){if(e&&j.isDragging&&!f){var t=e.target||g.parentNode,o=t.scrollLeft-t._gsScrollX,n=t.scrollTop-t._gsScrollY;(o||n)&&(X?(b-=o*X.a+n*X.c,w-=n*X.d+o*X.b):(b-=o,w-=n),t._gsScrollX+=o,t._gsScrollY+=n,ge(j.pointerX,j.pointerY))}}function Li(e){var t=Ye(),o=t-se<100,n=t-ee<50,i=o&&I===se,r=j.pointerEvent&&j.pointerEvent.defaultPrevented,a=o&&c===se,s=e.isTrusted||null==e.isTrusted&&o&&i;if((i||n&&!1!==j.vars.suppressClickOnDrag)&&e.stopImmediatePropagation&&e.stopImmediatePropagation(),o&&(!j.pointerEvent||!j.pointerEvent.defaultPrevented)&&(!i||s&&!a))return s&&i&&(c=se),void(I=se);(j.isPressed||n||o)&&(s&&e.detail&&o&&!r||Ba(e)),o||n||z||(e&&e.target&&(j.pointerEvent=e),Qa(j,"click","onClick"))}function Mi(e){return X?{x:e.x*X.a+e.y*X.c+X.e,y:e.x*X.b+e.y*X.d+X.f}:{x:e.x,y:e.y}}var u,f,b,w,T,M,D,h,l,k,S,C,E,m,v,P,L,t,N,R,A,x,y,X,B,Y,H,O,I,c,F,d,z,o=(p.type||"x,y").toLowerCase(),G=~o.indexOf("x")||~o.indexOf("y"),W=-1!==o.indexOf("rotation"),V=W?"rotation":G?"x":"left",Q=G?"y":"top",U=!(!~o.indexOf("x")&&!~o.indexOf("left")&&"scroll"!==o),K=!(!~o.indexOf("y")&&!~o.indexOf("top")&&"scroll"!==o),q=p.minimumMovement||2,j=_assertThisInitialized(e),J=_e(p.trigger||p.handle||g),n={},ee=0,te=!1,oe=p.autoScrollMarginTop||40,ne=p.autoScrollMarginRight||40,ie=p.autoScrollMarginBottom||40,re=p.autoScrollMarginLeft||40,ae=p.clickableTest||Va,se=0,le=g._gsap||me.core.getCache(g),ce=function _isFixed(e){return"fixed"===Na(e).position||((e=e.parentNode)&&1===e.nodeType?_isFixed(e):void 0)}(g),de=g.ownerDocument||xe,pe=function isTweening(){return j.tween&&j.tween.isActive()},ue=function onMove(e){var t,o,n,i,r,a,s=e;if(u&&!De&&j.isPressed&&e){if(t=(j.pointerEvent=e).changedTouches){if((e=t[0])!==m&&e.identifier!==v){for(i=t.length;-1<--i&&(e=t[i]).identifier!==v&&e.target!==g;);if(i<0)return}}else if(e.pointerId&&v&&e.pointerId!==v)return;y&&Y&&!H&&(We.x=e.pageX-(ce?Ga(de):0),We.y=e.pageY-(ce?Fa(de):0),X&&X.apply(We,We),o=We.x,n=We.y,((r=Math.abs(o-b))!==(a=Math.abs(n-w))&&(q<r||q<a)||ke&&Y===H)&&(H=a<r&&U?"x":"y",Y&&H!==Y&&za(ve,"touchforcechange",Ba),!1!==j.vars.lockAxisOnTouchScroll&&U&&K&&(j.lockedAxis="x"===H?"y":"x",Z(j.vars.onLockAxis)&&j.vars.onLockAxis.call(j,s)),ke&&Y===H))?fe(s):(F=j.allowEventDefault||Y&&(!H||Y===H)||!1===s.cancelable?F&&!1:(Ba(s),!0),j.autoScroll&&(te=!0),ge(e.pageX,e.pageY,l))}else F&&e&&u&&Ba(e)},ge=function setPointerPosition(e,t,o){var n,i,r,a,s,l,c=1-j.dragResistance,d=1-j.edgeResistance,p=j.pointerX,u=j.pointerY,g=M,f=j.x,h=j.y,m=j.endX,v=j.endY,x=j.endRotation,y=L;j.pointerX=e,j.pointerY=t,ce&&(e-=Ga(de),t-=Fa(de)),W?(a=Math.atan2(P.y-t,e-P.x)*Xe,180<(s=j.y-a)?(M-=360,j.y=a):s<-180&&(M+=360,j.y=a),r=j.x!==T||Math.max(Math.abs(b-e),Math.abs(w-t))>q?(j.y=a,T+(M-a)*c):T):(X&&(l=e*X.a+t*X.c+X.e,t=e*X.b+t*X.d+X.f,e=l),(i=t-w)<q&&-q<i&&(i=0),(n=e-b)<q&&-q<n&&(n=0),(j.lockAxis||j.lockedAxis)&&(n||i)&&((l=j.lockedAxis)||(j.lockedAxis=l=U&&Math.abs(n)>Math.abs(i)?"y":K?"x":null,l&&Z(j.vars.onLockAxis)&&j.vars.onLockAxis.call(j,j.pointerEvent)),"y"===l?i=0:"x"===l&&(n=0)),r=da(T+n*c),a=da(M+i*c)),(N||R||A)&&(j.x!==r||j.y!==a&&!W)&&(A&&(ze.x=r,ze.y=a,l=A(ze),r=da(l.x),a=da(l.y)),N&&(r=da(N(r))),R&&(a=da(R(a)))),D&&(k<r?r=k+Math.round((r-k)*d):r<S&&(r=S+Math.round((r-S)*d)),W||(C<a?a=Math.round(C+(a-C)*d):a<E&&(a=Math.round(E+(a-E)*d)))),j.x===r&&(j.y===a||W)||(W?(j.endRotation=j.x=j.endX=r,L=!0):(K&&(j.y=j.endY=a,L=!0),U&&(j.x=j.endX=r,L=!0)),o&&!1===Qa(j,"move","onMove")?(j.pointerX=p,j.pointerY=u,M=g,j.x=f,j.y=h,j.endX=m,j.endY=v,j.endRotation=x,L=y):!j.isDragging&&j.isPressed&&(j.isDragging=z=!0,Qa(j,"dragstart","onDragStart")))},fe=function onRelease(e,t){if(u&&j.isPressed&&(!e||null==v||t||!(e.pointerId&&e.pointerId!==v&&e.target!==g||e.changedTouches&&!function _hasTouchID(e,t){for(var o=e.length;o--;)if(e[o].identifier===t)return!0}(e.changedTouches,v)))){j.isPressed=!1;var o,n,i,r,a,s=e,l=j.isDragging,c=j.vars.allowContextMenu&&e&&(e.ctrlKey||2<e.which),d=me.delayedCall(.001,Fi);if(y?(Aa(y,"touchend",onRelease),Aa(y,"touchmove",ue),Aa(y,"touchcancel",onRelease),Aa(de,"touchstart",Ea)):Aa(de,"mousemove",ue),Aa(ve,"touchforcechange",Ba),Ee&&y||(Aa(de,"mouseup",onRelease),e&&e.target&&Aa(e.target,"mouseup",onRelease)),L=!1,l&&(ee=Fe=Ye(),j.isDragging=!1),xa(ui),x&&!c)return e&&(Aa(e.target,"change",onRelease),j.pointerEvent=s),Wa(J,!1),Qa(j,"release","onRelease"),Qa(j,"click","onClick"),void(x=!1);for(n=J.length;-1<--n;)Ma(J[n],"cursor",p.cursor||(!1!==p.cursor?Ce:null));if(Ne--,e){if((o=e.changedTouches)&&(e=o[0])!==m&&e.identifier!==v){for(n=o.length;-1<--n&&(e=o[n]).identifier!==v&&e.target!==g;);if(n<0&&!t)return}j.pointerEvent=s,j.pointerX=e.pageX,j.pointerY=e.pageY}return c&&s?(Ba(s),F=!0,Qa(j,"release","onRelease")):s&&!l?(F=!1,B&&(p.snap||p.bounds)&&Bi(p.inertia||p.throwProps),Qa(j,"release","onRelease"),ke&&"touchmove"===s.type||-1!==s.type.indexOf("cancel")||(Qa(j,"click","onClick"),Ye()-se<300&&Qa(j,"doubleclick","onDoubleClick"),r=s.target||g,se=Ye(),a=function syntheticClick(){se===I||!j.enabled()||j.isPressed||s.defaultPrevented||(r.click?r.click():de.createEvent&&((i=de.createEvent("MouseEvents")).initMouseEvent("click",!0,!0,ve,1,j.pointerEvent.screenX,j.pointerEvent.screenY,j.pointerX,j.pointerY,!1,!1,!1,!1,0,null),r.dispatchEvent(i)))},ke||s.defaultPrevented||me.delayedCall(.05,a))):(Bi(p.inertia||p.throwProps),j.allowEventDefault||!s||!1===p.dragClickables&&ae.call(j,s.target)||!l||Y&&(!H||Y!==H)||!1===s.cancelable?F=!1:(F=!0,Ba(s)),Qa(j,"release","onRelease")),pe()&&d.duration(j.tween.duration()),l&&Qa(j,"dragend","onDragEnd"),!0}F&&e&&u&&Ba(e)};return(t=Draggable.get(g))&&t.kill(),e.startDrag=function(e,t){var o,n,i,r;Gi(e||j.pointerEvent,!0),t&&!j.hitTest(e||j.pointerEvent)&&(o=Pa(e||j.pointerEvent),n=Pa(g),i=Mi({x:o.left+o.width/2,y:o.top+o.height/2}),r=Mi({x:n.left+n.width/2,y:n.top+n.height/2}),b-=i.x-r.x,w-=i.y-r.y),j.isDragging||(j.isDragging=z=!0,Qa(j,"dragstart","onDragStart"))},e.drag=ue,e.endDrag=function(e){return fe(e||j.pointerEvent,!0)},e.timeSinceDrag=function(){return j.isDragging?0:(Ye()-ee)/1e3},e.timeSinceClick=function(){return(Ye()-se)/1e3},e.hitTest=function(e,t){return Draggable.hitTest(j.target,e,t)},e.getDirection=function(e,t){var o,n,i,r,a,s,l="velocity"===e&&Se?e:$(e)&&!W?"element":"start";return"element"===l&&(a=Pa(j.target),s=Pa(e)),o="start"===l?j.x-T:"velocity"===l?Se.getVelocity(g,V):a.left+a.width/2-(s.left+s.width/2),W?o<0?"counter-clockwise":"clockwise":(t=t||2,n="start"===l?j.y-M:"velocity"===l?Se.getVelocity(g,Q):a.top+a.height/2-(s.top+s.height/2),r=(i=Math.abs(o/n))<1/t?"":o<0?"left":"right",i<t&&(""!==r&&(r+="-"),r+=n<0?"up":"down"),r)},e.applyBounds=function(e,t){var o,n,i,r,a,s;if(e&&p.bounds!==e)return p.bounds=e,j.update(!0,t);if(vi(!0),yi(),D&&!pe()){if(o=j.x,n=j.y,k<o?o=k:o<S&&(o=S),C<n?n=C:n<E&&(n=E),(j.x!==o||j.y!==n)&&(i=!0,j.x=j.endX=o,W?j.endRotation=o:j.y=j.endY=n,ui(L=!0),j.autoScroll&&!j.isDragging))for(La(g.parentNode),r=g,Ge.scrollTop=null!=ve.pageYOffset?ve.pageYOffset:null!=de.documentElement.scrollTop?de.documentElement.scrollTop:de.body.scrollTop,Ge.scrollLeft=null!=ve.pageXOffset?ve.pageXOffset:null!=de.documentElement.scrollLeft?de.documentElement.scrollLeft:de.body.scrollLeft;r&&!s;)a=(s=Ze(r.parentNode))?Ge:r.parentNode,K&&a.scrollTop>a._gsMaxScrollY&&(a.scrollTop=a._gsMaxScrollY),U&&a.scrollLeft>a._gsMaxScrollX&&(a.scrollLeft=a._gsMaxScrollX),r=a;j.isThrowing&&(i||j.endX>k||j.endX<S||j.endY>C||j.endY<E)&&Bi(p.inertia||p.throwProps,i)}return j},e.update=function(e,t,o){if(t&&j.isPressed){var n=getGlobalMatrix(g),i=d.apply({x:j.x-T,y:j.y-M}),r=getGlobalMatrix(g.parentNode,!0);r.apply({x:n.e-i.x,y:n.f-i.y},i),j.x-=i.x-r.e,j.y-=i.y-r.f,ui(!0),Di()}var a=j.x,s=j.y;return Ci(!t),e?j.applyBounds():(L&&o&&ui(!0),vi(!0)),t&&(ge(j.pointerX,j.pointerY),L&&ui(!0)),j.isPressed&&!t&&(U&&.01<Math.abs(a-j.x)||K&&.01<Math.abs(s-j.y)&&!W)&&Di(),j.autoScroll&&(La(g.parentNode,j.isDragging),te=j.isDragging,ui(!0),Ia(g,Ki),Ha(g,Ki)),j},e.enable=function(e){var t,o,n,i={lazy:!0};if(!1!==p.cursor&&(i.cursor=p.cursor||Ce),me.utils.checkPrefix("touchCallout")&&(i.touchCallout="none"),"soft"!==e){for(ta(J,U==K?"none":p.allowNativeTouchScrolling&&g.scrollHeight===g.clientHeight==(g.scrollWidth===g.clientHeight)||p.allowEventDefault?"manipulation":U?"pan-y":"pan-x"),o=J.length;-1<--o;)n=J[o],Ee||za(n,"mousedown",Gi),za(n,"touchstart",Gi),za(n,"click",Li,!0),me.set(n,i),n.getBBox&&n.ownerSVGElement&&U!=K&&me.set(n.ownerSVGElement,{touchAction:p.allowNativeTouchScrolling||p.allowEventDefault?"manipulation":U?"pan-y":"pan-x"}),p.allowContextMenu||za(n,"contextmenu",ti);Wa(J,!1)}return Ha(g,Ki),u=!0,Se&&"soft"!==e&&Se.track(f||g,G?"x,y":W?"rotation":"top,left"),g._gsDragID=t=g._gsDragID||"d"+Ie++,Oe[t]=j,f&&(f.enable(),f.element._gsDragID=t),(p.bounds||W)&&Di(),p.bounds&&j.applyBounds(),j},e.disable=function(e){for(var t,o=j.isDragging,n=J.length;-1<--n;)Ma(J[n],"cursor",null);if("soft"!==e){for(ta(J,null),n=J.length;-1<--n;)t=J[n],Ma(t,"touchCallout",null),Aa(t,"mousedown",Gi),Aa(t,"touchstart",Gi),Aa(t,"click",Li,!0),Aa(t,"contextmenu",ti);Wa(J,!0),y&&(Aa(y,"touchcancel",fe),Aa(y,"touchend",fe),Aa(y,"touchmove",ue)),Aa(de,"mouseup",fe),Aa(de,"mousemove",ue)}return Ia(g,Ki),u=!1,Se&&"soft"!==e&&(Se.untrack(f||g,G?"x,y":W?"rotation":"top,left"),j.tween&&j.tween.kill()),f&&f.disable(),xa(ui),j.isDragging=j.isPressed=x=!1,o&&Qa(j,"dragend","onDragEnd"),j},e.enabled=function(e,t){return arguments.length?e?j.enable(t):j.disable(t):u},e.kill=function(){return j.isThrowing=!1,j.tween&&j.tween.kill(),j.disable(),me.set(J,{clearProps:"userSelect"}),delete Oe[g._gsDragID],j},e.revert=function(){this.kill(),this.styles&&this.styles.revert()},~o.indexOf("scroll")&&(f=e.scrollProxy=new $a(g,function _extend(e,t){for(var o in t)o in e||(e[o]=t[o]);return e}({onKill:function onKill(){j.isPressed&&fe(null)}},p)),g.style.overflowY=K&&!Te?"auto":"hidden",g.style.overflowX=U&&!Te?"auto":"hidden",g=f.content),W?n.rotation=1:(U&&(n[V]=1),K&&(n[Q]=1)),le.force3D=!("force3D"in p)||p.force3D,Pe(_assertThisInitialized(e)),e.enable(),e}!function _setDefaults(e,t){for(var o in t)o in e||(e[o]=t[o])}(W.prototype,{pointerX:0,pointerY:0,startX:0,startY:0,deltaX:0,deltaY:0,isDragging:!1,isPressed:!1}),W.zIndex=1e3,W.version="3.13.0",Y()&&me.registerPlugin(W);function sb(){return"undefined"!=typeof window}function tb(){return V||sb()&&(V=window.gsap)&&V.registerPlugin&&V}function ub(e){return"string"==typeof e}function vb(e){return"function"==typeof e}function xb(e){return void 0===e}function Eb(e,t,o){var n=U.createElementNS?U.createElementNS("svg"===e?z:G,e):U.createElement(e);return t&&(ub(t)&&(t=U.querySelector(t)),t.appendChild(n)),"svg"===e&&(n.setAttribute("xmlns",z),n.setAttribute("xmlns:xlink",G)),o&&(n.style.cssText=o),n}function Fb(){U.selection?U.selection.empty():I.getSelection&&I.getSelection().removeAllRanges()}function Hb(e,t){var o=0,n=Math.max(0,e._repeat),i=e._first;for(i||(o=e.duration());i;)o=Math.max(o,999<i.totalDuration()?i.endTime(!1):i._start+i._tDur/i._ts),i=i._next;return!t&&n?o*(n+1)+e._rDelay*n:o}function Jb(e,t,o,n){var i,r,a;return ub(e)&&("="===e.charAt(1)?((i=parseInt(e.charAt(0)+"1",10)*parseFloat(e.substr(2)))<0&&0===n&&(n=100),e=n/100*t.duration()+i):isNaN(e)&&t.labels&&-1!==t.labels[e]?e=t.labels[e]:t===q&&(0<(r=e.indexOf("="))?(i=parseInt(e.charAt(r-1)+"1",10)*parseFloat(e.substr(r+1)),e=e.substr(0,r-1)):i=0,(a=V.getById(e))&&(e=function _globalizeTime(e,t){for(var o=e,n=1<arguments.length?+t:o.rawTime();o;)n=o._start+n/(o._ts||1),o=o.parent;return n}(a,o/100*a.duration())+i))),e=isNaN(e)?o:parseFloat(e),Math.min(100,Math.max(0,e/t.duration()*100))}function Nb(t,e,o,n){var i,r;if("mousedown"!==e&&"mouseup"!==e||(t.style.cursor="pointer"),"mousedown"===e&&(r=xb(t.onpointerdown)?xb(t.ontouchstart)?null:"touchstart":"pointerdown"))return i=function handler(e){"select"!==e.target.nodeName.toLowerCase()&&e.type===r?(e.stopPropagation(),pe&&(e.preventDefault(),o.call(t,e))):e.type!==r&&o.call(t,e),pe=!0},t.addEventListener(r,i,n),void("pointerdown"!==r&&t.addEventListener(e,i,n));t.addEventListener(e,o,n)}function Ob(e,t,o){e.removeEventListener(t,o),(t="mousedown"!==t?null:xb(e.onpointerdown)?xb(e.ontouchstart)?null:"touchstart":"pointerdown")&&e.removeEventListener(t,o)}function Pb(e,t,o,n){var i,r=e.options,a=r.length;for(t+="";-1<--a;)if(r[a].innerHTML===t||r[a].value===t)return e.selectedIndex=a,o.innerHTML=r[a].innerHTML,r[a];n&&((i=Eb("option",e)).setAttribute("value",t),i.innerHTML=o.innerHTML=ub(n)?n:t,e.selectedIndex=r.length-1)}function Qb(e,t,o){var n=e.options,i=Math.min(n.length-1,Math.max(0,e.selectedIndex+t));return e.selectedIndex=i,o&&(o.innerHTML=n[i].innerHTML),n[i].value}function Rb(){var e,t,o,n=F._first;if(ee){for(e=q._dur;n;)t=n._next,o=n._targets&&n._targets[0],vb(o)&&o===n.vars.onComplete&&!n._dur||o&&o._gsIgnore||q.add(n,n._start-n._delay),n=t;return e!==q.duration()}}function Ub(e){return V.getById(e)||oe.getById(e)||e===q.vars.id&&q}function Vb(e){V=e||tb(),Q||V&&sb()&&(U=document,K=U.documentElement,I=window,ie=V.core.context||function(){},V.registerPlugin(W),(F=V.globalTimeline)._sort=!0,F.autoRemoveChildren=!1,j=V.core.Animation,(oe=V.timeline({data:"indy",autoRemoveChildren:!0,smoothChildTiming:!0})).kill(),oe._dp=0,oe.to({},{duration:1e12}),q=V.timeline({data:"root",id:"Global Timeline",autoRemoveChildren:!1,smoothChildTiming:!0,parent:oe},0),J=V.to(q,{duration:1,time:1,ease:"none",data:"root",id:"_rootTween",paused:!0,immediateRender:!1,parent:oe},0),F.killTweensOf=function(e,t,o){q.killTweensOf(e,t,o),q.killTweensOf.call(F,e,t,o)},oe._start=V.ticker.time,V.ticker.add(function(e){return oe.render(e-oe._start)}),F._start+=F._time,q._start=F._time=F._tTime=0,(ne=function _delayedCall(e,t,o,n){return V.to(t,{delay:e,duration:0,onComplete:t,onReverseComplete:t,onCompleteParams:o,onReverseCompleteParams:o,callbackScope:n,parent:oe},oe._time)})(.01,function(){return ee?ee.update():Rb()}),ne(2,function(){var e,t,o;if(!ee)for(Rb(),e=q._first,o=q._start;e;)t=e._next,e._tDur!==e._tTime||!e._dur&&1!==e.progress()?F.add(e,e._start-e._delay+o):e.kill(),e=t;2<ue.globalRecordingTime?ne(ue.globalRecordingTime-2,function(){ee&&ee.update(),F.autoRemoveChildren=!0}):F.autoRemoveChildren=!0,ae=!1}),Q=1)}function Wb(e,t){t.globalSync||e.parent===F||F.add(e,F.time())}var V,Q,U,K,I,q,j,J,ee,te,F,oe,ne,ie,re,ae=!0,se=0,z="http://www.w3.org/2000/svg",G="http://www.w3.org/1999/xhtml",le=0,ce={},de=function(){try{return sessionStorage.setItem("gsTest","1"),sessionStorage.removeItem("gsTest"),!0}catch(e){return!1}}(),pe=!0,ue=function GSDevTools(r){Q||(Vb(),V||console.warn("Please gsap.registerPlugin(GSDevTools)")),this.vars=r=r||{},r.animation&&(GSDevTools.getByAnimation(r.animation)||{kill:function kill(){return 0}}).kill(),r.id=r.id||(ub(r.animation)?r.animation:le++),ce[r.id+""]=this,"globalSync"in r||(r.globalSync=!r.animation);function Zn(e){return n.querySelector(e)}function $n(e,t){return!1!==r.persist&&de&&sessionStorage.setItem("gs-dev-"+e+r.id,t),t}function _n(e){var t;if(!1!==r.persist&&de)return t=sessionStorage.getItem("gs-dev-"+e+r.id),"animation"===e?t:"loop"===e?"true"===t:parseFloat(t)}function Ho(c,d,p){return function(e){var t,o=y.getBoundingClientRect(),n=c.getBoundingClientRect(),i=n.width*d,r=V.getProperty(c,"x"),a=o.left-n.left-i+r,s=o.right-n.right+(n.width-i)+r,l=a;p&&(c!==M&&(t=M.getBoundingClientRect()).left&&(a+=t.left+t.width-o.left),c!==D&&(t=D.getBoundingClientRect()).left&&(s-=o.left+o.width-t.left)),m=P,this.applyBounds({minX:a,maxX:s}),u=v.duration()/o.width,g=-l*u,h?v.pause():v.pause(g+u*this.x),this.target===x&&(this.activated&&(this.allowEventDefault=!1),this.activated=!0),f=!0}}function Jo(){k=0,S=100,M.style.left="0%",D.style.left="100%",$n("in",k),$n("out",S),H(!0)}function No(e){if(!X.isPressed){var t=e.target.getBoundingClientRect(),o=((e.changedTouches?e.changedTouches[0]:e).clientX-t.left)/t.width*100;if(o<k)return k=o=Math.max(0,o),M.style.left=k+"%",void B.startDrag(e);if(S<o)return S=o=Math.min(100,o),D.style.left=S+"%",void Y.startDrag(e);v.progress(o/100).pause(),H(!0),X.startDrag(e)}}function Ro(){if(!X.isPressed){Wb(v,r);var e=v._targets&&v._targets[0];e===i&&e.seek(s+(l-s)*k/100),v.progress(k/100,!0),P||v.resume()}}function So(e){if($n("loop",d=e),d){if(N.play(),v.progress()>=S/100){var t=v._targets&&v._targets[0];t===i&&t.seek(s+(l-s)*k/100),i._repeat&&!k&&100===S?v.totalProgress(0,!0):v.progress(k/100,!0),O()}}else N.reverse()}function To(){return So(!d)}function Uo(){var e,t,o=function _getChildrenOf(e,t){for(var o=[],n=0,i=V.core.Tween,r=e._first;r;)r instanceof i?r.vars.id&&(o[n++]=r):(t&&r.vars.id&&(o[n++]=r),n=(o=o.concat(_getChildrenOf(r,t))).length),r=r._next;return o}(a&&!r.globalSync?a:q,!0),n=C.children,i=0;for(a&&!r.globalSync?o.unshift(a):r.hideGlobalTimeline||o.unshift(q),t=0;t<o.length;t++)(e=n[t]||Eb("option",C)).animation=o[t],i=t&&o[t].vars.id===o[t-1].vars.id?i+1:0,e.setAttribute("value",e.innerHTML=o[t].vars.id+(i?" ["+i+"]":o[t+1]&&o[t+1].vars.id===o[t].vars.id?" [0]":""));for(;t<n.length;t++)C.removeChild(n[t])}function Vo(e){var t,o,n=parseFloat(R.options[R.selectedIndex].value)||1;if(!arguments.length)return i;if(ub(e)&&(e=Ub(e)),e instanceof j||console.warn("GSDevTools error: invalid animation."),e.scrollTrigger&&console.warn("GSDevTools can't work with ScrollTrigger-based animations; either the scrollbar -OR- the GSDevTools scrubber can control the animation."),e!==i){if(i&&(i._inProgress=k,i._outProgress=S),i=e,v&&(n=v.timeScale(),v._targets&&v._targets[0]===a&&(a.resume(),v.kill())),k=i._inProgress||0,S=i._outProgress||100,M.style.left=k+"%",D.style.left=S+"%",c&&($n("animation",i.vars.id),$n("in",k),$n("out",S)),s=0,o=r.maxDuration||Math.min(1e3,Hb(i)),i===q||r.globalSync){if(Rb(),v=J,ee&&ee!==p&&console.warn("Error: GSDevTools can only have one instance that's globally synchronized."),ee=p,i!==q)for(99999999<(l=(t=i).totalDuration())&&(l=t.duration());t.parent;)s=s/t._ts+t._start,l=l/t._ts+t._start,t=t.parent;else l=q.duration();o<l-s&&(l=s+o),q.pause(s),J.vars.time=l,J.invalidate(),J.duration(l-s).timeScale(n),P?J.progress(1,!0).pause(0,!0):ne(.01,function(){J.resume().progress(k/100),P&&O()})}else{if(ee===p&&(ee=null),s=Math.min(k*i.duration(),i.time()),i!==a&&a){for(99999999<(l=(t=i).totalDuration())&&(l=t.duration());t.parent.parent&&t!==a;)s=s/(t._ts||t._pauseTS)+t._start,l=l/(t._ts||t._pauseTS)+t._start,t=t.parent;o<l-s&&(l=s+o),a.pause(s),v=V.to(a,{duration:l-s,time:l,ease:"none",data:"root",parent:oe},oe._time)}else v=i,!d&&v._repeat&&So(!0);v.timeScale(n),J.pause(),q.resume(),v.seek(0)}_.innerHTML=v.duration().toFixed(2),Pb(C,i.vars.id,E)}}function Xo(e){Vo(C.options[C.selectedIndex].animation),e.target&&e.target.blur&&e.target.blur(),P&&O()}function Yo(){var e,t=parseFloat(R.options[R.selectedIndex].value)||1;v.timeScale(t),$n("timeScale",t),P||(v.progress()>=S/100?((e=v._targets&&v._targets[0])===i&&e.seek(s+(l-s)*k/100),v.progress(k/100,!0).pause()):v.pause(),ne(.01,function(){return v.resume()})),A.innerHTML=t+"x",R.blur&&R.blur()}function _o(e){W.hitTest(e,n)||X.isDragging||B.isDragging||Y.isDragging||Z.restart(!0)}function ap(){G||(z.play(),Z.pause(),G=!0)}function bp(){Z.pause(),G&&(z.reverse(),G=!1)}function ep(e){ae&&!se&&(se=q._start),c=!e,(a=function _parseAnimation(e){return e instanceof j?e:e?V.getById(e):null}(r.animation))&&!a.vars.id&&(a.vars.id="[no id]"),Rb(),Uo();var t=Ub(_n("animation"));t&&(t._inProgress=_n("in")||0,t._outProgress=_n("out")||100),r.paused&&I(),i=null,Vo(a||t||q);var o=r.timeScale||_n("timeScale"),n=t===i;o&&(Pb(R,o,A,o+"x"),v.timeScale(o)),100===(k=("inTime"in r?Jb(r.inTime,i,0,0):n?t._inProgress:0)||0)&&!r.animation&&t&&(Vo(q),k=Jb(r.inTime,i,0,0)||0),k&&(M.style.left=k+"%",M.style.display=D.style.display="block"),(S=("outTime"in r?Jb(r.outTime,i,100,k):n?t._outProgress:0)||100)<k&&(S=100),100!==S&&(D.style.left=S+"%",M.style.display=D.style.display="block"),(d="loop"in r?r.loop:_n("loop"))&&So(!0),r.paused&&v.progress(k/100,!0).pause(),ae&&i===q&&se&&r.globalSync&&!P&&v.time(-se,!0),H(!0)}var u,g,f,h,m,i,v,a,s,l,c,e,d,p=this,n=function _createRootElement(e,t,o){re||(Eb("style",K).innerHTML=".gs-dev-tools{height:51px;bottom:0;left:0;right:0;display:block;position:fixed;overflow:visible;padding:0;font-size:15px;font-family:-apple-system,BlinkMacSystemFont,avenir next,sans-serif;color:#bbbaa6}.gs-dev-tools *{box-sizing:content-box;visibility:visible}.gs-dev-tools .gs-top{position:relative;z-index:499}.gs-dev-tools .gs-bottom{display:flex;align-items:center;justify-content:space-between;gap:1rem;background-color:#0e100f;height:42px;position:relative}.gs-dev-tools .timeline{position:relative;height:8px;margin-left:15px;margin-right:15px;overflow:visible}.gs-dev-tools .progress-bar,.gs-dev-tools .timeline-track{height:8px;position:absolute;top:0;left:-15px;right:-15px}.gs-dev-tools .timeline-track{background-color:#222}.gs-dev-tools .progress-bar{background:linear-gradient(114.41deg,#0ae448 20.74%,#abff84 65.5%);height:8px;top:0;width:0;pointer-events:none}.gs-dev-tools .seek-bar{width:100%;position:absolute;height:24px;top:-12px;left:0;background-color:transparent}.gs-dev-tools .in-point,.gs-dev-tools .out-point{width:15px;height:26px;position:absolute;top:-18px}.gs-dev-tools .in-point-shape{fill:#0ae448;transform:translateX(1px)}.gs-dev-tools .out-point-shape{fill:#ff8709}.gs-dev-tools .in-point{transform:translateX(-100%)}.gs-dev-tools .out-point{left:100%}.gs-dev-tools .playhead{position:absolute;top:-5px;transform:translate(-50%,0);left:0;border-radius:50%;width:16px;height:16px;background:linear-gradient(114.41deg,#0ae448 20.74%,#abff84 65.5%)}.gs-dev-tools .gs-btn-white{fill:#fffce1}.gs-dev-tools .pause{opacity:0}.gs-dev-tools .select-animation{vertical-align:middle;position:relative;padding:6px 10px}.gs-dev-tools .select-animation-container{flex-grow:4;width:40%}.gs-dev-tools .select-arrow{display:inline-block;width:12px;height:7px;margin:0 7px;transform:translate(0,-2px)}.gs-dev-tools .select-arrow-shape{stroke:currentcolor;stroke-width:2px;fill:none}.gs-dev-tools .rewind{height:14px}.gs-dev-tools .ease-border,.gs-dev-tools .rewind-path{fill:currentColor}.gs-dev-tools .play-pause{width:18px;height:18px}.gs-dev-tools .ease{width:20px;height:20px;min-width:30px;display:none}.gs-dev-tools .ease-path{fill:none;stroke:#abff84;stroke-width:2px}.gs-dev-tools .time-scale{text-align:center;min-width:30px}.gs-dev-tools .loop{width:15px}.gs-dev-tools label span{text-decoration:none}.gs-dev-tools button:focus,.gs-dev-tools select:focus{outline:0}.gs-dev-tools label{position:relative;cursor:pointer}.gs-dev-tools label.locked{text-decoration:none;cursor:auto}.gs-dev-tools label input,.gs-dev-tools label select{position:absolute;left:0;top:0;z-index:1;font:inherit;font-size:inherit;line-height:inherit;height:100%;width:100%;color:#000!important;opacity:0;background:0 0;border:none;padding:0;margin:0;-webkit-appearance:none;-moz-appearance:none;appearance:none;cursor:pointer}.gs-dev-tools label input+.display{position:relative;z-index:2}.gs-dev-tools .gs-bottom-right{vertical-align:middle;display:flex;align-items:center;flex-grow:4;width:40%;justify-content:flex-end}.gs-dev-tools .time-container{margin:0 5px}.gs-dev-tools .logo{width:32px;height:32px;position:relative;top:2px;margin:0 12px}.gs-dev-tools .gs-hit-area{background-color:transparent;width:100%;height:100%;top:0;position:absolute}.gs-dev-tools.minimal{height:auto;display:flex;align-items:stretch}.gs-dev-tools.minimal .gs-top{order:2;flex-grow:4;background-color:#000}.gs-dev-tools.minimal .gs-bottom{background-color:#0e100f;border-top:none}.gs-dev-tools.minimal .timeline{top:50%;transform:translate(0,-50%)}.gs-dev-tools.minimal .gs-bottom-right,.gs-dev-tools.minimal .in-point,.gs-dev-tools.minimal .out-point,.gs-dev-tools.minimal .rewind,.gs-dev-tools.minimal .select-animation-container{display:none}.gs-dev-tools.minimal .play-pause{width:20px;height:20px;padding:4px 6px;margin-left:14px}.gs-dev-tools.minimal .time-scale{min-width:26px}.gs-dev-tools.minimal .loop{width:18px;min-width:18px;display:none}@media only screen and (max-width:600px){.gs-dev-tools{height:auto;display:flex;align-items:stretch}.gs-dev-tools .gs-top{order:2;flex-grow:4;background-color:#000;height:42px}.gs-dev-tools .gs-bottom{background-color:#000;border-top:none}.gs-dev-tools .timeline{top:50%;transform:translate(0,-50%)}.gs-dev-tools .gs-bottom-right,.gs-dev-tools .in-point,.gs-dev-tools .out-point,.gs-dev-tools .rewind,.gs-dev-tools .select-animation-container{display:none}.gs-dev-tools .play-pause{width:18px;height:18px;padding:4px 6px;margin-left:14px}.gs-dev-tools .time-scale{min-width:26px}.gs-dev-tools .loop{width:18px;min-width:18px;display:none}.gs-dev-tools .progress-bar,.gs-dev-tools .timeline-track{right:0}}",re=!0),ub(e)&&(e=U.querySelector(e));var n=Eb("div",e||K.getElementsByTagName("body")[0]||K);return n.setAttribute("class","gs-dev-tools"+(t?" minimal":"")),n.innerHTML='<div class=gs-hit-area></div><div class=gs-top><div class=timeline><div class=timeline-track></div><div class=progress-bar></div><div class=seek-bar></div><svg class=in-point viewBox="0 0 15 26" xmlns=http://www.w3.org/2000/svg><path class=in-point-shape d="M0.5,2.283c0,-0.985 0.798,-1.783 1.783,-1.783c2.679,0 7.717,0 10.41,0c0.48,-0 0.939,0.19 1.278,0.529c0.339,0.339 0.529,0.798 0.529,1.277c-0,4.821 -0,17.897 0,21.968c0,0.253 -0.135,0.488 -0.354,0.615c-0.22,0.128 -0.49,0.128 -0.711,0.003c-2.653,-1.517 -9.526,-5.444 -12.016,-6.867c-0.568,-0.325 -0.919,-0.929 -0.919,-1.583c-0,-2.835 -0,-10.627 -0,-14.159Z" style="fill:#00ff52;fill-rule:nonzero;"/></svg><svg class=out-point viewBox="0 0 15 26" xmlns=http://www.w3.org/2000/svg><path class=out-point-shape d="M0.5,2.251c0,-0.465 0.184,-0.91 0.513,-1.238c0.328,-0.329 0.773,-0.513 1.238,-0.513c2.669,0 7.733,0 10.439,0c0.48,-0 0.94,0.191 1.28,0.53c0.339,0.34 0.53,0.8 0.53,1.28l0,14.17c-0,0.631 -0.338,1.213 -0.886,1.526c-2.44,1.395 -9.262,5.293 -11.977,6.845c-0.236,0.134 -0.524,0.133 -0.759,-0.003c-0.234,-0.136 -0.378,-0.386 -0.378,-0.657c0,-4.178 0,-17.198 0,-21.94Z" style="fill-rule:nonzero;"/></svg><div class=playhead></div></div></div><div class=gs-bottom><div class=select-animation-container><label class=select-animation><select class=animation-list><option>Global Timeline<option>myTimeline</select><nobr><span class="display animation-label">Global Timeline</span><svg class=select-arrow viewBox="0 0 12.05 6.73" xmlns=http://www.w3.org/2000/svg><polyline class=select-arrow-shape points="0.35 0.35 6.03 6.03 11.7 0.35"/></svg></nobr></label></div><svg class=rewind viewBox="0 0 12 15.38" xmlns=http://www.w3.org/2000/svg><path d=M0,.38H2v15H0Zm2,7,10,7.36V0Z class="gs-btn-white rewind-path"/></svg><svg class=play-pause viewBox="0 0 20.97 25.67" xmlns=http://www.w3.org/2000/svg><g class=play><path d="M8,4.88 C8,10.18 8,15.48 8,20.79 5.33,22.41 2.66,24.04 0,25.67 0,17.11 0,8.55 0,0 2.66,1.62 5.33,3.25 8,4.88" class="gs-btn-white play-1" style=stroke:#fffce1;stroke-width:.6px /><path d="M14.485,8.855 C16.64,10.18 18.8,11.5 20.97,12.83 16.64,15.48 12.32,18.13 8,20.79 8,15.48 8,10.18 8,4.88 10.16,6.2 12.32,7.53 14.48,8.85" class="gs-btn-white play-2" style=stroke:#fffce1;stroke-width:.6px /></g></svg> <svg class=loop viewBox="0 0 29 25.38" xmlns=http://www.w3.org/2000/svg fill="currentcolor"><path d=M27.44,5.44,20.19,0V3.06H9.06A9.31,9.31,0,0,0,0,12.41,9.74,9.74,0,0,0,.69,16l3.06-2.23a6,6,0,0,1-.12-1.22,5.49,5.49,0,0,1,5.43-5.5H20.19v3.81Z class=loop-path /><path d=M25.25,11.54a5.18,5.18,0,0,1,.12,1.12,5.41,5.41,0,0,1-5.43,5.41H9.19V14.5L1.94,19.94l7.25,5.44V22.06H19.94A9.2,9.2,0,0,0,29,12.84a9.42,9.42,0,0,0-.68-3.53Z class=loop-path /></svg> <svg class=ease viewBox="0 0 25.67 25.67" xmlns=http://www.w3.org/2000/svg><path d=M.48,25.12c1.74-3.57,4.28-12.6,8.8-10.7s4.75,1.43,6.5-1.11S19.89,1.19,25.2.55 class=ease-path /><path d=M24.67,1V24.67H1V1H24.67m1-1H0V25.67H25.67V0Z class=ease-border /></svg><label class=time-scale><select><option value=10>10x<option value=5>5x<option value=2>2x<option value=1 selected>1x<option value=0.5>0.5x<option value=0.25>0.25x<option value=0.1>0.1x</select><span class="display time-scale-label">1x</span></label><div class=gs-bottom-right><div class=time-container><span class=time>0.00</span> / <span class=duration>0.00</span></div><a href="https://gsap.com/docs/v3/Plugins/GSDevTools?source=GSDevTools" target=_blank title=Docs><svg class="logo" viewBox="0 0 1080 1080" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M793 518.654C793 518.654 793 518.829 793 518.934L770.197 618.386C768.967 624.012 763.38 628.135 756.915 628.135H729.404C727.366 628.135 725.539 629.498 724.977 631.455C699.573 718.082 665.175 777.628 619.745 813.376C581.095 843.813 533.451 858 469.819 858C412.618 858 374.074 839.514 341.362 803.032C298.145 754.809 280.261 675.869 291.083 580.75C310.618 402.184 402.745 222.01 580.217 222.01C634.185 221.521 676.559 238.26 706.039 271.667C737.204 306.995 753.05 360.216 753.121 429.86C753.015 436.185 747.78 441.287 741.491 441.287H611.488C606.885 441.287 602.774 437.129 602.844 432.551C601.79 384.397 587.56 360.915 559.311 360.915C509.488 360.915 480.079 428.917 464.479 466.622C442.694 519.283 431.627 576.452 433.805 633.412C434.824 659.935 439.075 697.22 464.127 712.666C486.333 726.364 518.026 717.279 537.21 702.113C556.394 686.947 571.819 660.703 578.319 636.766C579.233 633.447 579.303 630.861 578.425 629.708C577.511 628.52 574.981 628.24 573.049 628.24H539.67C536.086 628.24 532.537 626.598 530.394 623.942C528.602 621.705 527.899 618.945 528.532 616.219L551.37 516.592C552.494 511.49 557.097 507.647 562.754 506.948V506.703H781.827C782.354 506.703 782.881 506.703 783.373 506.808C789.065 507.542 793.07 512.853 792.965 518.654H793Z" fill="#0AE448"/></svg></a></div></div>',e&&(n.style.position="absolute",n.style.top=t?"calc(100% - 42px)":"calc(100% - 51px)"),o&&(ub(o)?n.style.cssText=o:function _isObject(e){return"object"==typeof e}(o)&&(o.data="root",V.set(n,o).kill()),n.style.top&&(n.style.bottom="auto"),n.style.width&&V.set(n,{xPercent:-50,left:"50%",right:"auto",data:"root"}).kill()),!t&&n.offsetWidth<600&&(n.setAttribute("class","gs-dev-tools minimal"),e&&(n.style.top="calc(100% - 42px)")),n}(r.container,r.minimal,r.css),x=Zn(".playhead"),y=Zn(".timeline-track"),b=Zn(".progress-bar"),w=Zn(".time"),_=Zn(".duration"),T=0,M=Zn(".in-point"),D=Zn(".out-point"),k=0,S=100,C=Zn(".animation-list"),E=Zn(".animation-label"),t=Zn(".play-pause"),o=function _buildPlayPauseMorph(e){var t=V.timeline({data:"root",parent:oe,onComplete:function onComplete(){return t.kill()}},oe._time);return t.to(e.querySelector(".play-1"),{duration:.4,attr:{d:"M5.75,3.13 C5.75,9.79 5.75,16.46 5.75,23.13 4.08,23.13 2.41,23.13 0.75,23.13 0.75,16.46 0.75,9.79 0.75,3.12 2.41,3.12 4.08,3.12 5.75,3.12"},ease:"power2.inOut",rotation:360,transformOrigin:"50% 50%"}).to(e.querySelector(".play-2"),{duration:.4,attr:{d:"M16.38,3.13 C16.38,9.79 16.38,16.46 16.38,23.13 14.71,23.13 13.04,23.13 11.38,23.13 11.38,16.46 11.38,9.79 11.38,3.12 13.04,3.12 14.71,3.12 16.38,3.12"},ease:"power2.inOut",rotation:360,transformOrigin:"50% 50%"},.05),t}(t),P=!1,L=Zn(".loop"),N=function _buildLoopAnimation(e){var t=V.timeline({data:"root",id:"loop",parent:oe,paused:!0,onComplete:function onComplete(){return t.kill()}},oe._time);return t.to(e,{duration:.5,rotation:360,ease:"power3.inOut",transformOrigin:"50% 50%"}).to(e.querySelectorAll(".loop-path"),{duration:.5,fill:"#91e600",ease:"none"},0),t}(L),R=Zn(".time-scale select"),A=Zn(".time-scale-label"),X=W.create(x,{type:"x",cursor:"ew-resize",allowNativeTouchScrolling:!1,allowEventDefault:!0,onPress:Ho(x,.5,!0),onDrag:function onDrag(){var e=g+u*this.x;e<0?e=0:e>v._dur&&(e=v._dur),h||v.time(e),b.style.width=Math.min(S-k,Math.max(0,e/v._dur*100-k))+"%",w.innerHTML=e.toFixed(2)},onRelease:function onRelease(){P||v.resume()}})[0],B=W.create(M,{type:"x",cursor:"ew-resize",zIndexBoost:!1,allowNativeTouchScrolling:!1,allowEventDefault:!0,onPress:Ho(M,1,!0),onDoubleClick:Jo,onDrag:function onDrag(){k=(g+u*this.x)/v.duration()*100,v.progress(k/100),H(!0)},onRelease:function onRelease(){k<0&&(k=0),Fb(),M.style.left=k+"%",$n("in",k),V.set(M,{x:0,data:"root",display:"block"}),P||v.resume()}})[0],Y=W.create(D,{type:"x",cursor:"ew-resize",allowNativeTouchScrolling:!1,allowEventDefault:!0,zIndexBoost:!1,onPress:Ho(D,0,!0),onDoubleClick:Jo,onDrag:function onDrag(){S=(g+u*this.x)/v.duration()*100,v.progress(S/100),H(!0)},onRelease:function onRelease(){100<S&&(S=100),Fb(),D.style.left=S+"%",$n("out",S),V.set(D,{x:0,data:"root",display:"block"}),m||(O(),v.resume())}})[0],H=function updateProgress(e){if(!X.isPressed||!0===e){var t,o=d||-1!==i._repeat?100*v.progress()||0:i.totalTime()/i.duration()*100,n=i._repeat&&i._rDelay&&i.totalTime()%(i.duration()+i._rDelay)>i.duration();100<o&&(o=100),S<=o?!d||v.paused()||X.isDragging?(o===S&&-1!==i._repeat||(o=S,v.progress(o/100)),!P&&(S<100||1===i.totalProgress()||-1===i._repeat)&&I()):n||(o=k,(t=v._targets&&v._targets[0])===i&&t.seek(s+(l-s)*k/100),0<i._repeat&&!k&&100===S?1===i.totalProgress()&&v.totalProgress(0,!0).resume():v.progress(o/100,!0).resume()):o<k&&(o=k,v.progress(o/100,!0)),o===T&&!0!==e||(b.style.left=k+"%",b.style.width=Math.max(0,o-k)+"%",x.style.left=o+"%",w.innerHTML=v._time.toFixed(2),_.innerHTML=v._dur.toFixed(2),f&&(x.style.transform="translate(-50%,0)",x._gsap.x="0px",x._gsap.xPercent=-50,f=!1),T=o),v.paused()!==P&&F()}},O=function play(){if(v.progress()>=S/100){Wb(v,r);var e=v._targets&&v._targets[0];e===i&&e.seek(s+(l-s)*k/100),v._repeat&&!k?v.totalProgress(0,!0):v.reversed()||v.progress(k/100,!0)}o.play(),v.resume(),P&&p.update(),P=!1},I=function pause(){o.reverse(),v&&v.pause(),P=!0},F=function togglePlayPause(){(P?O:I)()},z=V.to([Zn(".gs-bottom"),Zn(".gs-top")],{duration:.3,autoAlpha:0,y:50,ease:"power2.in",data:"root",paused:!0,parent:oe},oe._time),G=!1,Z=ne(1.3,ap).pause();Nb(C,"change",Xo),Nb(C,"mousedown",Uo),Nb(t,"mousedown",F),Nb(Zn(".seek-bar"),"mousedown",No),Nb(Zn(".rewind"),"mousedown",Ro),Nb(L,"mousedown",To),Nb(R,"change",Yo),"auto"===r.visibility?(Nb(n,"mouseout",_o),Nb(n,"mouseover",bp)):"hidden"===r.visibility&&(G=!0,z.progress(1)),!1!==r.keyboard&&(te&&r.keyboard?console.warn("[GSDevTools warning] only one instance can be affected by keyboard shortcuts. There is already one active."):(te=p,Nb(K,"keydown",e=function keyboardHandler(e){var t,o=e.keyCode?e.keyCode:e.which;32===o?F():38===o?(t=parseFloat(Qb(R,-1,A)),v.timeScale(t),$n("timeScale",t)):40===o?(t=parseFloat(Qb(R,1,A)),v.timeScale(t),$n("timeScale",t)):37===o?Ro():39===o?v.progress(S/100):76===o?To():72===o?function toggleHide(){(G?bp:ap)()}():73===o?(k=100*v.progress(),$n("in",k),M.style.left=k+"%",H(!0)):79===o&&(S=100*v.progress(),$n("out",S),D.style.left=S+"%",H(!0))}))),V.set(x,{xPercent:-50,x:0,data:"root"}),V.set(M,{xPercent:-100,x:0,data:"root"}),M._gsIgnore=D._gsIgnore=x._gsIgnore=t._gsIgnore=L._gsIgnore=!0,V.killTweensOf([M,D,x]),ep(ae),ae&&ne(1e-4,ep,[!1],this),V.ticker.add(H),this.update=function(e){ee===p&&(J.paused()&&!e||Rb(),function updateRootDuration(){var e,t,o;i===q&&(e=q._time,q.progress(1,!0).time(e,!0),e=(J._dp._time-J._start)*J._ts,1e3===(o=Math.min(1e3,q.duration()))&&(o=Math.min(1e3,Hb(q))),1!=(t=J.duration()/o)&&o&&(k*=t,S<100&&(S*=t),J.seek(0),J.vars.time=o,J.invalidate(),J.duration(o),J.time(e),_.innerHTML=o.toFixed(2),M.style.left=k+"%",D.style.left=S+"%",H(!0)))}())},this.kill=this.revert=function(){Ob(C,"change",Xo),Ob(C,"mousedown",Uo),Ob(t,"mousedown",F),Ob(Zn(".seek-bar"),"mousedown",No),Ob(Zn(".rewind"),"mousedown",Ro),Ob(L,"mousedown",To),Ob(R,"change",Yo),X.disable(),B.disable(),Y.disable(),V.ticker.remove(H),Ob(n,"mouseout",_o),Ob(n,"mouseover",bp),n.parentNode.removeChild(n),ee===p&&(ee=null),te===p&&(te=null,Ob(K,"keydown",e)),delete ce[r.id+""]},this.minimal=function(e){var t,o=n.classList.contains("minimal");if(!arguments.length||o===e)return o;e?n.classList.add("minimal"):n.classList.remove("minimal"),r.container&&(n.style.top=e?"calc(100% - 42px)":"calc(100% - 51px)"),X.isPressed&&(h=!0,X.endDrag(X.pointerEvent),h=!1,t=100*v.progress(),b.style.width=Math.max(0,t-k)+"%",x.style.left=t+"%",x.style.transform="translate(-50%,0)",x._gsap.x="0px",x._gsap.xPercent=-50,X.startDrag(X.pointerEvent,!0))},this.animation=Vo,this.updateList=Uo,ie(this)};ue.version="3.13.0",ue.globalRecordingTime=2,ue.getById=function(e){return e?ce[e]:ee},ue.getByAnimation=function(e){for(var t in ub(e)&&(e=V.getById(e)),ce)if(ce[t].animation()===e)return ce[t]},ue.create=function(e){return new ue(e)},ue.register=Vb,tb()&&V.registerPlugin(ue),e.GSDevTools=ue,e.default=ue;if (typeof(window)==="undefined"||window!==e){Object.defineProperty(e,"__esModule",{value:!0})} else {delete e.default}});

