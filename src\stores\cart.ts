import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface Product {
  id: number
  name: string
  price: number
  image: string
  description: string
  category: string
  weight: string
  origin: string
}

export interface CartItem {
  product: Product
  quantity: number
}

export const useCartStore = defineStore('cart', () => {
  const items = ref<CartItem[]>([])
  const isOpen = ref(false)

  // Computed properties
  const itemCount = computed(() => {
    return items.value.reduce((total, item) => total + item.quantity, 0)
  })

  const totalPrice = computed(() => {
    return items.value.reduce((total, item) => total + (item.product.price * item.quantity), 0)
  })

  const isEmpty = computed(() => items.value.length === 0)

  // Actions
  const addToCart = (product: Product, quantity: number = 1) => {
    const existingItem = items.value.find(item => item.product.id === product.id)
    
    if (existingItem) {
      existingItem.quantity += quantity
    } else {
      items.value.push({
        product,
        quantity
      })
    }
    
    // Save to localStorage
    saveToLocalStorage()
  }

  const removeFromCart = (productId: number) => {
    const index = items.value.findIndex(item => item.product.id === productId)
    if (index > -1) {
      items.value.splice(index, 1)
      saveToLocalStorage()
    }
  }

  const updateQuantity = (productId: number, quantity: number) => {
    const item = items.value.find(item => item.product.id === productId)
    if (item) {
      if (quantity <= 0) {
        removeFromCart(productId)
      } else {
        item.quantity = quantity
        saveToLocalStorage()
      }
    }
  }

  const clearCart = () => {
    items.value = []
    saveToLocalStorage()
  }

  const toggleCart = () => {
    isOpen.value = !isOpen.value
  }

  const closeCart = () => {
    isOpen.value = false
  }

  const openCart = () => {
    isOpen.value = true
  }

  // Local storage functions
  const saveToLocalStorage = () => {
    localStorage.setItem('mutlu-elmalar-cart', JSON.stringify(items.value))
  }

  const loadFromLocalStorage = () => {
    const saved = localStorage.getItem('mutlu-elmalar-cart')
    if (saved) {
      try {
        items.value = JSON.parse(saved)
      } catch (error) {
        console.error('Error loading cart from localStorage:', error)
        items.value = []
      }
    }
  }

  // Initialize cart from localStorage
  loadFromLocalStorage()

  return {
    items,
    isOpen,
    itemCount,
    totalPrice,
    isEmpty,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    toggleCart,
    closeCart,
    openCart,
    loadFromLocalStorage
  }
})
