<template>
  <div class="about-page">
    <!-- Hero Section -->
    <section class="about-hero parallax">
      <div class="hero-background">
        <div class="hero-image"></div>
        <div class="hero-overlay"></div>
      </div>
      <div class="container">
        <div class="hero-content">
          <h1 class="hero-title fade-in">Hakkımızda</h1>
          <p class="hero-subtitle fade-in">Doğanın en güzel hediyelerini sizlere sunuyoruz</p>
        </div>
      </div>
    </section>

    <!-- Story Section -->
    <section class="section story-section">
      <div class="container">
        <div class="story-content grid grid-2">
          <div class="story-text">
            <h2 class="section-title">Hikayemiz</h2>
            <p>
              Mutlu <PERSON>alar, 2019 yılında küçük bir aile işletmesi olarak başladı.
              Isparta'nın verimli topraklarında, geleneksel tarım yöntemlerini modern tekniklerle birleştirerek,
              en kaliteli elmaları yetiştirme tutkusuyla yola çıktık.
            </p>
            <p>
              <PERSON><PERSON><PERSON><PERSON>, 5 yıllık deneyimimizle Türkiye'nin dört bir yanına organik ve taze elmalar gönderiyoruz.
              Her elmamız özenle seçilir, hijyenik koşullarda paketlenir ve aynı gün içinde size ulaştırılır.
            </p>
            <p>
              Misyonumuz, doğal ve sağlıklı beslenmeyi desteklemek, müşterilerimize en taze ve lezzetli
              elmaları sunarak mutlu sofralara katkıda bulunmaktır.
            </p>
          </div>
          <div class="story-image">
            <img src="https://images.unsplash.com/photo-1542838132-92c53300491e?w=600&h=400&fit=crop"
                 alt="Elma bahçesi" class="image-overlay" />
          </div>
        </div>
      </div>
    </section>

    <!-- Values Section -->
    <section class="section values-section">
      <div class="container">
        <h2 class="section-title">Değerlerimiz</h2>
        <div class="values-grid grid grid-3">
          <div class="value-item card hover-lift">
            <div class="value-icon">🌱</div>
            <h3>Doğallık</h3>
            <p>Kimyasal gübre ve pestisit kullanmadan, tamamen organik yöntemlerle üretim yapıyoruz.</p>
          </div>
          <div class="value-item card hover-lift">
            <div class="value-icon">💚</div>
            <h3>Kalite</h3>
            <p>Her elmamız titizlikle seçilir ve kalite kontrolünden geçer. Sadece en iyisini sunuyoruz.</p>
          </div>
          <div class="value-item card hover-lift">
            <div class="value-icon">🤝</div>
            <h3>Güven</h3>
            <p>Müşteri memnuniyeti bizim için öncelik. Şeffaf ve dürüst iş anlayışımızla güven veriyoruz.</p>
          </div>
          <div class="value-item card hover-lift">
            <div class="value-icon">🚀</div>
            <h3>Yenilik</h3>
            <p>Modern tarım teknikleri ve sürdürülebilir üretim yöntemleriyle sürekli gelişiyoruz.</p>
          </div>
          <div class="value-item card hover-lift">
            <div class="value-icon">🌍</div>
            <h3>Çevre</h3>
            <p>Doğaya saygılı üretim anlayışımızla gelecek nesillere temiz bir dünya bırakıyoruz.</p>
          </div>
          <div class="value-item card hover-lift">
            <div class="value-icon">❤️</div>
            <h3>Sevgi</h3>
            <p>Her elmamızı sevgiyle yetiştiriyor, özenle paketliyor ve mutlulukla gönderiyoruz.</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Team Section -->
    <section class="section team-section">
      <div class="container">
        <h2 class="section-title">Ekibimiz</h2>
        <div class="team-grid grid grid-3">
          <div class="team-member card hover-lift">
            <div class="member-image">
              <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop"
                   alt="Ahmet Yılmaz" />
            </div>
            <div class="member-info">
              <h3>Ahmet Yılmaz</h3>
              <p class="member-role">Kurucu & CEO</p>
              <p class="member-description">
                20 yıllık tarım deneyimi ile şirketimizin vizyonunu belirliyor ve kalite standartlarımızı yönetiyor.
              </p>
            </div>
          </div>
          <div class="team-member card hover-lift">
            <div class="member-image">
              <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=300&h=300&fit=crop"
                   alt="Fatma Demir" />
            </div>
            <div class="member-info">
              <h3>Fatma Demir</h3>
              <p class="member-role">Üretim Müdürü</p>
              <p class="member-description">
                Ziraat mühendisi olan Fatma, organik tarım süreçlerimizi yönetiyor ve kalite kontrolünü sağlıyor.
              </p>
            </div>
          </div>
          <div class="team-member card hover-lift">
            <div class="member-image">
              <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300&h=300&fit=crop"
                   alt="Mehmet Kaya" />
            </div>
            <div class="member-info">
              <h3>Mehmet Kaya</h3>
              <p class="member-role">Lojistik Müdürü</p>
              <p class="member-description">
                Elmalarımızın taze olarak müşterilerimize ulaşmasını sağlayan lojistik operasyonlarımızı yönetiyor.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Stats Section -->
    <section class="section stats-section parallax">
      <div class="parallax-content">
        <div class="container">
          <h2 class="section-title">Rakamlarla Mutlu Elmalar</h2>
          <div class="stats-grid grid grid-4">
            <div class="stat-item">
              <div class="stat-number">
                <AnimatedCounter :target="1000" suffix="+" />
              </div>
              <div class="stat-label">Mutlu Müşteri</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">
                <AnimatedCounter :target="50" suffix="+" />
              </div>
              <div class="stat-label">Ton Elma Üretimi</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">
                <AnimatedCounter :target="8" />
              </div>
              <div class="stat-label">Farklı Çeşit</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">
                <AnimatedCounter :target="5" />
              </div>
              <div class="stat-label">Yıllık Deneyim</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Contact CTA Section -->
    <section class="section contact-cta-section">
      <div class="container">
        <div class="cta-content">
          <h2>Bizimle İletişime Geçin</h2>
          <p>Sorularınız mı var? Özel siparişleriniz için bizimle iletişime geçin!</p>
          <div class="cta-buttons">
            <RouterLink to="/contact" class="btn btn-primary">İletişim</RouterLink>
            <RouterLink to="/products" class="btn btn-secondary">Ürünleri İncele</RouterLink>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import AnimatedCounter from '@/components/AnimatedCounter.vue'

gsap.registerPlugin(ScrollTrigger)

onMounted(() => {
  // Hero animations
  gsap.from('.hero-title', {
    duration: 1,
    y: 50,
    opacity: 0,
    ease: 'power2.out'
  })

  gsap.from('.hero-subtitle', {
    duration: 0.8,
    y: 30,
    opacity: 0,
    ease: 'power2.out',
    delay: 0.3
  })

  // Story section animation
  gsap.from('.story-text > *', {
    scrollTrigger: {
      trigger: '.story-section',
      start: 'top 80%',
      end: 'bottom 20%',
      toggleActions: 'play none none reverse'
    },
    duration: 0.8,
    y: 30,
    opacity: 0,
    stagger: 0.2,
    ease: 'power2.out'
  })

  gsap.from('.story-image', {
    scrollTrigger: {
      trigger: '.story-section',
      start: 'top 80%',
      end: 'bottom 20%',
      toggleActions: 'play none none reverse'
    },
    duration: 1,
    x: 50,
    opacity: 0,
    ease: 'power2.out'
  })

  // Values section animation
  gsap.from('.value-item', {
    scrollTrigger: {
      trigger: '.values-section',
      start: 'top 80%',
      end: 'bottom 20%',
      toggleActions: 'play none none reverse'
    },
    duration: 0.6,
    y: 30,
    opacity: 0,
    stagger: 0.1,
    ease: 'power2.out'
  })

  // Team section animation
  gsap.from('.team-member', {
    scrollTrigger: {
      trigger: '.team-section',
      start: 'top 80%',
      end: 'bottom 20%',
      toggleActions: 'play none none reverse'
    },
    duration: 0.8,
    y: 50,
    opacity: 0,
    stagger: 0.2,
    ease: 'power2.out'
  })

  // Stats section animation
  gsap.from('.stat-item', {
    scrollTrigger: {
      trigger: '.stats-section',
      start: 'top 80%',
      end: 'bottom 20%',
      toggleActions: 'play none none reverse'
    },
    duration: 0.6,
    scale: 0,
    opacity: 0,
    stagger: 0.1,
    ease: 'back.out(1.7)'
  })
})
</script>

<style scoped>
.about-page {
  padding-top: 80px; /* Account for fixed navbar */
}

/* Hero Section */
.about-hero {
  position: relative;
  min-height: 60vh;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -2;
}

.hero-image {
  position: absolute;
  top: -20%;
  left: 0;
  right: 0;
  bottom: -20%;
  background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(243, 156, 18, 0.1)),
              url('https://images.unsplash.com/photo-**********-1e4cd0b6cbd6?w=1920&h=1080&fit=crop') center/cover;
  will-change: transform;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  z-index: -1;
}

.hero-content {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.hero-title {
  color: var(--text-dark);
  margin-bottom: 1rem;
}

.hero-subtitle {
  font-size: 1.3rem;
  color: var(--text-dark);
  opacity: 0.8;
  margin: 0;
}

/* Story Section */
.story-section {
  background: white;
}

.story-content {
  align-items: center;
  gap: 4rem;
}

.story-text {
  max-width: none;
}

.story-text .section-title {
  text-align: left;
  margin-bottom: 2rem;
}

.story-text .section-title::after {
  left: 0;
  transform: none;
}

.story-text p {
  margin-bottom: 1.5rem;
  line-height: 1.8;
  color: var(--text-dark);
}

.story-image img {
  width: 100%;
  height: 400px;
  object-fit: cover;
  border-radius: var(--border-radius);
}

/* Values Section */
.values-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.values-grid {
  gap: 2rem;
}

.value-item {
  text-align: center;
  padding: 2rem;
}

.value-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.value-item h3 {
  color: var(--text-dark);
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.value-item p {
  color: var(--text-dark);
  opacity: 0.8;
  line-height: 1.6;
  margin: 0;
}

/* Team Section */
.team-section {
  background: white;
}

.team-grid {
  gap: 2rem;
}

.team-member {
  text-align: center;
  padding: 2rem;
}

.member-image {
  margin-bottom: 1.5rem;
}

.member-image img {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid var(--primary-color);
}

.member-info h3 {
  color: var(--text-dark);
  margin-bottom: 0.5rem;
  font-size: 1.3rem;
}

.member-role {
  color: var(--primary-color);
  font-weight: 600;
  margin-bottom: 1rem;
  font-size: 1rem;
}

.member-description {
  color: var(--text-dark);
  opacity: 0.8;
  line-height: 1.6;
  margin: 0;
}

/* Stats Section */
.stats-section {
  background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(243, 156, 18, 0.1)),
              url('https://images.unsplash.com/photo-1542838132-92c53300491e?w=1920&h=1080&fit=crop') center/cover;
}

.stats-section .parallax-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.stats-grid {
  gap: 2rem;
}

.stat-item {
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: var(--border-radius);
  box-shadow: 0 4px 20px var(--shadow-light);
}

.stat-number {
  font-size: 3rem;
  font-weight: 800;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.stat-label {
  color: var(--text-dark);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: 0.9rem;
}

/* Contact CTA Section */
.contact-cta-section {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
}

.cta-content {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.cta-content h2 {
  color: white;
  margin-bottom: 1rem;
}

.cta-content p {
  font-size: 1.2rem;
  opacity: 0.9;
  margin-bottom: 2rem;
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .story-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .story-text .section-title {
    text-align: center;
  }

  .story-text .section-title::after {
    left: 50%;
    transform: translateX(-50%);
  }

  .values-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  .team-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

@media (max-width: 768px) {
  .about-hero {
    min-height: 50vh;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .story-content {
    gap: 2rem;
  }

  .value-item,
  .team-member,
  .stat-item {
    padding: 1.5rem;
  }

  .value-icon {
    font-size: 2.5rem;
  }

  .member-image img {
    width: 120px;
    height: 120px;
  }

  .stat-number {
    font-size: 2.5rem;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .btn {
    width: 100%;
    max-width: 250px;
  }
}

@media (max-width: 480px) {
  .values-grid,
  .team-grid,
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .story-image img {
    height: 250px;
  }
}
</style>
