<template>
  <div class="products-page">
    <!-- Page Header -->
    <section class="page-header">
      <div class="container">
        <h1 class="page-title fade-in">Ürünlerimiz</h1>
        <p class="page-subtitle fade-in">En taze ve lezzetli elmalarımızı keşfedin</p>
      </div>
    </section>

    <!-- Filters and Search -->
    <section class="filters-section">
      <div class="container">
        <div class="filters-container">
          <!-- Search -->
          <div class="search-box">
            <input 
              type="text" 
              v-model="productsStore.searchQuery"
              placeholder="Elma ara..."
              class="search-input"
            />
            <span class="search-icon">🔍</span>
          </div>

          <!-- Category Filter -->
          <div class="category-filter">
            <button 
              v-for="category in productsStore.categories"
              :key="category"
              @click="productsStore.setCategory(category)"
              class="category-btn"
              :class="{ 'active': productsStore.selectedCategory === category }"
            >
              {{ category }}
            </button>
          </div>

          <!-- Results Count -->
          <div class="results-info">
            {{ productsStore.filteredProducts.length }} ürün bulundu
          </div>
        </div>
      </div>
    </section>

    <!-- Products Grid -->
    <section class="products-section">
      <div class="container">
        <Transition name="products-grid" mode="out-in">
          <div v-if="productsStore.filteredProducts.length > 0" class="products-grid grid grid-3">
            <ProductCard 
              v-for="product in productsStore.filteredProducts" 
              :key="product.id"
              :product="product"
              @quick-view="handleQuickView"
            />
          </div>
          <div v-else class="no-products">
            <div class="no-products-icon">😔</div>
            <h3>Ürün bulunamadı</h3>
            <p>Arama kriterlerinizi değiştirmeyi deneyin.</p>
            <button @click="clearFilters" class="btn btn-primary">
              Filtreleri Temizle
            </button>
          </div>
        </Transition>
      </div>
    </section>

    <!-- Quick View Modal -->
    <Transition name="modal">
      <div v-if="showQuickView" class="modal-overlay" @click="closeQuickView">
        <div class="modal-content" @click.stop>
          <button class="modal-close" @click="closeQuickView">&times;</button>
          <div v-if="quickViewProduct" class="quick-view-content">
            <div class="quick-view-image">
              <img :src="quickViewProduct.image" :alt="quickViewProduct.name" />
            </div>
            <div class="quick-view-info">
              <h3>{{ quickViewProduct.name }}</h3>
              <p>{{ quickViewProduct.description }}</p>
              <div class="quick-view-details">
                <p><strong>Kategori:</strong> {{ quickViewProduct.category }}</p>
                <p><strong>Ağırlık:</strong> {{ quickViewProduct.weight }}</p>
                <p><strong>Menşei:</strong> {{ quickViewProduct.origin }}</p>
              </div>
              <div class="quick-view-price">
                {{ formatPrice(quickViewProduct.price) }} / kg
              </div>
              <div class="quick-view-actions">
                <div class="quantity-selector">
                  <button @click="decreaseQuantity" :disabled="modalQuantity <= 1">-</button>
                  <span>{{ modalQuantity }}</span>
                  <button @click="increaseQuantity">+</button>
                </div>
                <button @click="addToCartFromModal" class="btn btn-primary">
                  Sepete Ekle
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import ProductCard from '@/components/ProductCard.vue'
import { useProductsStore } from '@/stores/products'
import { useCartStore } from '@/stores/cart'
import type { Product } from '@/stores/cart'

gsap.registerPlugin(ScrollTrigger)

const productsStore = useProductsStore()
const cartStore = useCartStore()

const quickViewProduct = ref<Product | null>(null)
const showQuickView = ref(false)
const modalQuantity = ref(1)

const formatPrice = (price: number): string => {
  return new Intl.NumberFormat('tr-TR', {
    style: 'currency',
    currency: 'TRY'
  }).format(price)
}

const handleQuickView = (product: Product) => {
  quickViewProduct.value = product
  showQuickView.value = true
  modalQuantity.value = 1
}

const closeQuickView = () => {
  showQuickView.value = false
  quickViewProduct.value = null
  modalQuantity.value = 1
}

const increaseQuantity = () => {
  modalQuantity.value++
}

const decreaseQuantity = () => {
  if (modalQuantity.value > 1) {
    modalQuantity.value--
  }
}

const addToCartFromModal = () => {
  if (quickViewProduct.value) {
    cartStore.addToCart(quickViewProduct.value, modalQuantity.value)
    closeQuickView()
  }
}

const clearFilters = () => {
  productsStore.setCategory('Tümü')
  productsStore.setSearchQuery('')
}

onMounted(() => {
  // Page entrance animations
  gsap.from('.page-title', {
    duration: 1,
    y: 50,
    opacity: 0,
    ease: 'power2.out'
  })

  gsap.from('.page-subtitle', {
    duration: 0.8,
    y: 30,
    opacity: 0,
    ease: 'power2.out',
    delay: 0.2
  })

  gsap.from('.filters-container > *', {
    duration: 0.6,
    y: 20,
    opacity: 0,
    stagger: 0.1,
    ease: 'power2.out',
    delay: 0.4
  })

  // Products grid animation
  gsap.from('.products-grid .product-card', {
    scrollTrigger: {
      trigger: '.products-grid',
      start: 'top 80%',
      end: 'bottom 20%',
      toggleActions: 'play none none reverse'
    },
    duration: 0.6,
    y: 30,
    opacity: 0,
    stagger: 0.1,
    ease: 'power2.out'
  })
})
</script>

<style scoped>
.products-page {
  padding-top: 80px; /* Account for fixed navbar */
}

/* Page Header */
.page-header {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  padding: 4rem 0;
  text-align: center;
}

.page-title {
  color: white;
  margin-bottom: 1rem;
}

.page-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  margin: 0;
}

/* Filters Section */
.filters-section {
  background: white;
  padding: 2rem 0;
  border-bottom: 1px solid #eee;
  position: sticky;
  top: 80px;
  z-index: 100;
}

.filters-container {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  align-items: center;
  justify-content: space-between;
}

.search-box {
  position: relative;
  flex: 1;
  max-width: 300px;
}

.search-input {
  width: 100%;
  padding: 0.8rem 1rem 0.8rem 3rem;
  border: 2px solid #eee;
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: var(--transition-fast);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.2rem;
  opacity: 0.5;
}

.category-filter {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.category-btn {
  padding: 0.6rem 1.2rem;
  border: 2px solid #eee;
  background: white;
  border-radius: 25px;
  cursor: pointer;
  transition: var(--transition-fast);
  font-weight: 500;
  white-space: nowrap;
}

.category-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.category-btn.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.results-info {
  color: var(--text-dark);
  opacity: 0.7;
  font-weight: 500;
}

/* Products Section */
.products-section {
  padding: 3rem 0;
}

.products-grid {
  gap: 2rem;
}

/* No Products */
.no-products {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--text-dark);
}

.no-products-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.no-products h3 {
  margin-bottom: 1rem;
  color: var(--text-dark);
}

.no-products p {
  margin-bottom: 2rem;
  opacity: 0.7;
}

/* Products Grid Transition */
.products-grid-enter-active,
.products-grid-leave-active {
  transition: all 0.3s ease;
}

.products-grid-enter-from,
.products-grid-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

/* Modal Styles (reused from HomeView) */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 2rem;
}

.modal-content {
  background: white;
  border-radius: var(--border-radius);
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

.modal-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  font-size: 2rem;
  cursor: pointer;
  color: var(--text-dark);
  z-index: 1;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-fast);
}

.modal-close:hover {
  background: var(--shadow-light);
}

.quick-view-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  padding: 2rem;
}

.quick-view-image img {
  width: 100%;
  height: 300px;
  object-fit: cover;
  border-radius: var(--border-radius);
}

.quick-view-info h3 {
  margin-bottom: 1rem;
  color: var(--text-dark);
}

.quick-view-details {
  margin: 1.5rem 0;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: var(--border-radius);
}

.quick-view-details p {
  margin-bottom: 0.5rem;
}

.quick-view-price {
  font-size: 1.5rem;
  font-weight: 800;
  color: var(--primary-color);
  margin: 1.5rem 0;
}

.quick-view-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.quantity-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #f8f9fa;
  border-radius: var(--border-radius);
  padding: 0.3rem;
}

.quantity-selector button {
  width: 30px;
  height: 30px;
  border: none;
  background: white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  transition: var(--transition-fast);
}

.quantity-selector button:hover:not(:disabled) {
  background: var(--primary-color);
  color: white;
}

.quantity-selector button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity-selector span {
  min-width: 30px;
  text-align: center;
  font-weight: 600;
}

/* Modal Transitions */
.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .filters-container {
    flex-direction: column;
    align-items: stretch;
    gap: 1.5rem;
  }
  
  .search-box {
    max-width: none;
  }
  
  .category-filter {
    justify-content: center;
  }
  
  .quick-view-content {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .filters-section {
    position: static;
  }
  
  .category-filter {
    justify-content: flex-start;
  }
  
  .quick-view-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .quantity-selector {
    align-self: center;
  }
}
</style>
