@import './base.css';

/* Global Variables */
:root {
  --primary-color: #e74c3c;
  --primary-dark: #c0392b;
  --secondary-color: #f39c12;
  --accent-color: #27ae60;
  --text-dark: #2c3e50;
  --text-light: #ecf0f1;
  --background-light: #ffffff;
  --background-dark: #34495e;
  --shadow-light: rgba(0, 0, 0, 0.1);
  --shadow-medium: rgba(0, 0, 0, 0.2);
  --shadow-heavy: rgba(0, 0, 0, 0.3);
  --border-radius: 12px;
  --transition-fast: 0.2s ease;
  --transition-medium: 0.4s ease;
  --transition-slow: 0.6s ease;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
  line-height: 1.6;
  color: var(--text-dark);
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

#app {
  min-height: 100vh;
  width: 100%;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
}

h1 {
  font-size: clamp(2.5rem, 5vw, 4rem);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

h2 {
  font-size: clamp(2rem, 4vw, 3rem);
  color: var(--text-dark);
}

h3 {
  font-size: clamp(1.5rem, 3vw, 2rem);
  color: var(--text-dark);
}

p {
  margin-bottom: 1rem;
  font-size: 1.1rem;
  line-height: 1.7;
}

/* Links and Buttons */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition-fast);
  position: relative;
}

a:hover {
  color: var(--primary-dark);
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 2rem;
  border: none;
  border-radius: var(--border-radius);
  font-size: 1.1rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition-medium);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: var(--transition-medium);
  z-index: -1;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  box-shadow: 0 4px 15px var(--shadow-medium);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--shadow-heavy);
}

.btn-secondary {
  background: linear-gradient(135deg, var(--secondary-color), #e67e22);
  color: white;
  box-shadow: 0 4px 15px var(--shadow-medium);
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--shadow-heavy);
}

/* Container and Layout */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.section {
  padding: 5rem 0;
  position: relative;
}

.section-title {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 2px;
}

/* Grid System */
.grid {
  display: grid;
  gap: 2rem;
}

.grid-2 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-4 {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* Card Styles */
.card {
  background: var(--background-light);
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: 0 4px 20px var(--shadow-light);
  transition: var(--transition-medium);
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  transform: scaleX(0);
  transition: var(--transition-medium);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px var(--shadow-medium);
}

.card:hover::before {
  transform: scaleX(1);
}

/* Animation Classes */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: var(--transition-slow);
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

.slide-in-left {
  opacity: 0;
  transform: translateX(-50px);
  transition: var(--transition-slow);
}

.slide-in-left.visible {
  opacity: 1;
  transform: translateX(0);
}

.slide-in-right {
  opacity: 0;
  transform: translateX(50px);
  transition: var(--transition-slow);
}

.slide-in-right.visible {
  opacity: 1;
  transform: translateX(0);
}

.scale-in {
  opacity: 0;
  transform: scale(0.8);
  transition: var(--transition-slow);
}

.scale-in.visible {
  opacity: 1;
  transform: scale(1);
}

/* Loading Animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% { transform: translate3d(0, 0, 0); }
  40%, 43% { transform: translate3d(0, -30px, 0); }
  70% { transform: translate3d(0, -15px, 0); }
  90% { transform: translate3d(0, -4px, 0); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--shadow-light);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.pulse {
  animation: pulse 2s infinite;
}

.bounce {
  animation: bounce 2s infinite;
}

.float {
  animation: float 3s ease-in-out infinite;
}

/* Parallax Effect */
.parallax {
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
}

.parallax::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 1;
}

.parallax-content {
  position: relative;
  z-index: 2;
  color: white;
  text-align: center;
  padding: 8rem 0;
}

/* Hover Effects */
.hover-lift {
  transition: var(--transition-medium);
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px var(--shadow-medium);
}

.hover-glow {
  transition: var(--transition-medium);
}

.hover-glow:hover {
  box-shadow: 0 0 20px var(--primary-color);
}

/* Image Effects */
.image-overlay {
  position: relative;
  overflow: hidden;
  border-radius: var(--border-radius);
}

.image-overlay img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition-medium);
}

.image-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  opacity: 0;
  transition: var(--transition-medium);
  z-index: 1;
}

.image-overlay:hover::before {
  opacity: 0.8;
}

.image-overlay:hover img {
  transform: scale(1.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .section {
    padding: 3rem 0;
  }

  .btn {
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
  }

  .card {
    padding: 1.5rem;
  }

  h1 {
    font-size: 2.5rem;
  }

  h2 {
    font-size: 2rem;
  }

  h3 {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 0.5rem;
  }

  .grid-2,
  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr;
  }

  .btn {
    width: 100%;
    padding: 1rem;
  }
}
