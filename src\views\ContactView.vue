<template>
  <div class="contact-page">
    <!-- Page Header -->
    <section class="page-header">
      <div class="container">
        <h1 class="page-title fade-in"><PERSON>letişim</h1>
        <p class="page-subtitle fade-in">Bizimle iletişime geçin, size yardımcı olmaktan mutluluk duyarız</p>
      </div>
    </section>

    <!-- Contact Content -->
    <section class="section contact-section">
      <div class="container">
        <div class="contact-content grid grid-2">
          <!-- Contact Form -->
          <div class="contact-form-container">
            <h2>Bize Mesaj Gönderin</h2>
            <form @submit.prevent="handleSubmit" class="contact-form">
              <div class="form-group">
                <label for="name">Ad Soyad *</label>
                <input 
                  type="text" 
                  id="name" 
                  v-model="form.name"
                  required
                  class="form-input"
                  :class="{ 'error': errors.name }"
                />
                <span v-if="errors.name" class="error-message">{{ errors.name }}</span>
              </div>

              <div class="form-group">
                <label for="email">E-posta *</label>
                <input 
                  type="email" 
                  id="email" 
                  v-model="form.email"
                  required
                  class="form-input"
                  :class="{ 'error': errors.email }"
                />
                <span v-if="errors.email" class="error-message">{{ errors.email }}</span>
              </div>

              <div class="form-group">
                <label for="phone">Telefon</label>
                <input 
                  type="tel" 
                  id="phone" 
                  v-model="form.phone"
                  class="form-input"
                />
              </div>

              <div class="form-group">
                <label for="subject">Konu *</label>
                <select 
                  id="subject" 
                  v-model="form.subject"
                  required
                  class="form-input"
                  :class="{ 'error': errors.subject }"
                >
                  <option value="">Konu seçin</option>
                  <option value="order">Sipariş</option>
                  <option value="product">Ürün Bilgisi</option>
                  <option value="complaint">Şikayet</option>
                  <option value="suggestion">Öneri</option>
                  <option value="other">Diğer</option>
                </select>
                <span v-if="errors.subject" class="error-message">{{ errors.subject }}</span>
              </div>

              <div class="form-group">
                <label for="message">Mesajınız *</label>
                <textarea 
                  id="message" 
                  v-model="form.message"
                  required
                  rows="5"
                  class="form-input"
                  :class="{ 'error': errors.message }"
                  placeholder="Mesajınızı buraya yazın..."
                ></textarea>
                <span v-if="errors.message" class="error-message">{{ errors.message }}</span>
              </div>

              <button 
                type="submit" 
                class="btn btn-primary submit-btn"
                :disabled="isSubmitting"
                :class="{ 'submitting': isSubmitting }"
              >
                <span v-if="!isSubmitting">Mesaj Gönder</span>
                <span v-else class="loading-spinner"></span>
              </button>
            </form>
          </div>

          <!-- Contact Info -->
          <div class="contact-info">
            <h2>İletişim Bilgileri</h2>
            
            <div class="info-item">
              <div class="info-icon">📍</div>
              <div class="info-content">
                <h3>Adres</h3>
                <p>Mutlu Elmalar Tarım Ltd. Şti.<br>
                   Bahçelievler Mah. Elma Sok. No: 15<br>
                   32200 Isparta / Türkiye</p>
              </div>
            </div>

            <div class="info-item">
              <div class="info-icon">📞</div>
              <div class="info-content">
                <h3>Telefon</h3>
                <p>+90 (246) 123 45 67<br>
                   +90 (246) 123 45 68</p>
              </div>
            </div>

            <div class="info-item">
              <div class="info-icon">📧</div>
              <div class="info-content">
                <h3>E-posta</h3>
                <p><EMAIL><br>
                   <EMAIL></p>
              </div>
            </div>

            <div class="info-item">
              <div class="info-icon">🕒</div>
              <div class="info-content">
                <h3>Çalışma Saatleri</h3>
                <p>Pazartesi - Cuma: 08:00 - 18:00<br>
                   Cumartesi: 09:00 - 16:00<br>
                   Pazar: Kapalı</p>
              </div>
            </div>

            <!-- Social Media -->
            <div class="social-media">
              <h3>Sosyal Medya</h3>
              <div class="social-links">
                <a href="#" class="social-link">📘 Facebook</a>
                <a href="#" class="social-link">📷 Instagram</a>
                <a href="#" class="social-link">🐦 Twitter</a>
                <a href="#" class="social-link">💼 LinkedIn</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Map Section -->
    <section class="section map-section">
      <div class="container">
        <h2 class="section-title">Konumumuz</h2>
        <div class="map-container">
          <div class="map-placeholder">
            <div class="map-content">
              <div class="map-icon">🗺️</div>
              <h3>Harita</h3>
              <p>Isparta, Bahçelievler Mahallesi</p>
              <p>Gerçek bir uygulamada burada interaktif harita olacaktır.</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section class="section faq-section">
      <div class="container">
        <h2 class="section-title">Sık Sorulan Sorular</h2>
        <div class="faq-list">
          <div 
            v-for="(faq, index) in faqs" 
            :key="index"
            class="faq-item"
            :class="{ 'active': activeFaq === index }"
          >
            <button 
              @click="toggleFaq(index)"
              class="faq-question"
            >
              {{ faq.question }}
              <span class="faq-icon">{{ activeFaq === index ? '−' : '+' }}</span>
            </button>
            <Transition name="faq-answer">
              <div v-if="activeFaq === index" class="faq-answer">
                <p>{{ faq.answer }}</p>
              </div>
            </Transition>
          </div>
        </div>
      </div>
    </section>

    <!-- Success Modal -->
    <Transition name="modal">
      <div v-if="showSuccessModal" class="modal-overlay" @click="closeSuccessModal">
        <div class="modal-content success-modal" @click.stop>
          <div class="success-icon">✅</div>
          <h3>Mesajınız Gönderildi!</h3>
          <p>En kısa sürede size geri dönüş yapacağız. Teşekkür ederiz!</p>
          <button @click="closeSuccessModal" class="btn btn-primary">Tamam</button>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

gsap.registerPlugin(ScrollTrigger)

// Form data
const form = reactive({
  name: '',
  email: '',
  phone: '',
  subject: '',
  message: ''
})

const errors = reactive({
  name: '',
  email: '',
  subject: '',
  message: ''
})

const isSubmitting = ref(false)
const showSuccessModal = ref(false)

// FAQ data
const activeFaq = ref<number | null>(null)
const faqs = ref([
  {
    question: 'Siparişim ne kadar sürede gelir?',
    answer: 'Siparişleriniz aynı gün içinde hazırlanır ve 24 saat içinde adresinize teslim edilir. Isparta ve çevre illerde aynı gün teslimat imkanımız bulunmaktadır.'
  },
  {
    question: 'Elmalar gerçekten organik mi?',
    answer: 'Evet, tüm elmalarımız organik sertifikalıdır. Kimyasal gübre ve pestisit kullanmadan, doğal yöntemlerle üretilmektedir. Sertifikalarımızı web sitemizden inceleyebilirsiniz.'
  },
  {
    question: 'Minimum sipariş miktarı var mı?',
    answer: 'Minimum sipariş miktarımız 5 kg\'dır. Daha küçük miktarlar için lojistik maliyetleri nedeniyle teslimat yapamıyoruz.'
  },
  {
    question: 'İade politikanız nedir?',
    answer: 'Ürünlerimizden memnun kalmazsanız, teslim tarihinden itibaren 3 gün içinde iade edebilirsiniz. Ürünlerin bozulmamış olması gerekmektedir.'
  },
  {
    question: 'Toptan satış yapıyor musunuz?',
    answer: 'Evet, restoranlar, marketler ve diğer işletmeler için toptan satış yapıyoruz. Özel fiyatlar için bizimle iletişime geçin.'
  }
])

// Form validation
const validateForm = () => {
  // Reset errors
  Object.keys(errors).forEach(key => {
    errors[key as keyof typeof errors] = ''
  })

  let isValid = true

  if (!form.name.trim()) {
    errors.name = 'Ad soyad gereklidir'
    isValid = false
  }

  if (!form.email.trim()) {
    errors.email = 'E-posta gereklidir'
    isValid = false
  } else if (!/\S+@\S+\.\S+/.test(form.email)) {
    errors.email = 'Geçerli bir e-posta adresi girin'
    isValid = false
  }

  if (!form.subject) {
    errors.subject = 'Konu seçimi gereklidir'
    isValid = false
  }

  if (!form.message.trim()) {
    errors.message = 'Mesaj gereklidir'
    isValid = false
  } else if (form.message.trim().length < 10) {
    errors.message = 'Mesaj en az 10 karakter olmalıdır'
    isValid = false
  }

  return isValid
}

// Form submission
const handleSubmit = async () => {
  if (!validateForm()) return

  isSubmitting.value = true

  // Simulate API call
  await new Promise(resolve => setTimeout(resolve, 2000))

  isSubmitting.value = false
  showSuccessModal.value = true

  // Reset form
  Object.keys(form).forEach(key => {
    form[key as keyof typeof form] = ''
  })
}

const closeSuccessModal = () => {
  showSuccessModal.value = false
}

// FAQ toggle
const toggleFaq = (index: number) => {
  activeFaq.value = activeFaq.value === index ? null : index
}

onMounted(() => {
  // Page entrance animations
  gsap.from('.page-title', {
    duration: 1,
    y: 50,
    opacity: 0,
    ease: 'power2.out'
  })

  gsap.from('.page-subtitle', {
    duration: 0.8,
    y: 30,
    opacity: 0,
    ease: 'power2.out',
    delay: 0.2
  })

  // Contact content animation
  gsap.from('.contact-form-container', {
    scrollTrigger: {
      trigger: '.contact-section',
      start: 'top 80%',
      end: 'bottom 20%',
      toggleActions: 'play none none reverse'
    },
    duration: 0.8,
    x: -50,
    opacity: 0,
    ease: 'power2.out'
  })

  gsap.from('.contact-info', {
    scrollTrigger: {
      trigger: '.contact-section',
      start: 'top 80%',
      end: 'bottom 20%',
      toggleActions: 'play none none reverse'
    },
    duration: 0.8,
    x: 50,
    opacity: 0,
    ease: 'power2.out'
  })

  // FAQ animation
  gsap.from('.faq-item', {
    scrollTrigger: {
      trigger: '.faq-section',
      start: 'top 80%',
      end: 'bottom 20%',
      toggleActions: 'play none none reverse'
    },
    duration: 0.6,
    y: 30,
    opacity: 0,
    stagger: 0.1,
    ease: 'power2.out'
  })
})
</script>

<style scoped>
.contact-page {
  padding-top: 80px; /* Account for fixed navbar */
}

/* Page Header */
.page-header {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  padding: 4rem 0;
  text-align: center;
}

.page-title {
  color: white;
  margin-bottom: 1rem;
}

.page-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  margin: 0;
}

/* Contact Section */
.contact-section {
  background: white;
}

.contact-content {
  gap: 4rem;
  align-items: start;
}

/* Contact Form */
.contact-form-container h2 {
  margin-bottom: 2rem;
  color: var(--text-dark);
}

.contact-form {
  max-width: none;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text-dark);
}

.form-input {
  width: 100%;
  padding: 1rem;
  border: 2px solid #eee;
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: var(--transition-fast);
  font-family: inherit;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
}

.form-input.error {
  border-color: #e74c3c;
}

.error-message {
  color: #e74c3c;
  font-size: 0.9rem;
  margin-top: 0.3rem;
  display: block;
}

.submit-btn {
  width: 100%;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  position: relative;
  overflow: hidden;
}

.submit-btn.submitting {
  pointer-events: none;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  display: inline-block;
}

/* Contact Info */
.contact-info h2 {
  margin-bottom: 2rem;
  color: var(--text-dark);
}

.info-item {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: var(--border-radius);
  transition: var(--transition-medium);
}

.info-item:hover {
  background: #e9ecef;
  transform: translateY(-2px);
}

.info-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
  margin-top: 0.2rem;
}

.info-content h3 {
  margin-bottom: 0.5rem;
  color: var(--text-dark);
  font-size: 1.1rem;
}

.info-content p {
  margin: 0;
  color: var(--text-dark);
  opacity: 0.8;
  line-height: 1.6;
}

.social-media {
  margin-top: 2rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: var(--border-radius);
}

.social-media h3 {
  margin-bottom: 1rem;
  color: var(--text-dark);
}

.social-links {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.social-link {
  color: var(--primary-color);
  text-decoration: none;
  padding: 0.5rem 0;
  transition: var(--transition-fast);
  border-bottom: 1px solid transparent;
}

.social-link:hover {
  color: var(--primary-dark);
  border-bottom-color: var(--primary-color);
}

/* Map Section */
.map-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.map-container {
  margin-top: 2rem;
}

.map-placeholder {
  height: 400px;
  background: white;
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed #ddd;
}

.map-content {
  text-align: center;
  color: var(--text-dark);
}

.map-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.map-content h3 {
  margin-bottom: 1rem;
  color: var(--text-dark);
}

.map-content p {
  margin-bottom: 0.5rem;
  opacity: 0.7;
}

/* FAQ Section */
.faq-section {
  background: white;
}

.faq-list {
  max-width: 800px;
  margin: 0 auto;
}

.faq-item {
  border: 1px solid #eee;
  border-radius: var(--border-radius);
  margin-bottom: 1rem;
  overflow: hidden;
  transition: var(--transition-medium);
}

.faq-item:hover {
  box-shadow: 0 4px 20px var(--shadow-light);
}

.faq-item.active {
  border-color: var(--primary-color);
}

.faq-question {
  width: 100%;
  padding: 1.5rem;
  background: none;
  border: none;
  text-align: left;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-dark);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: var(--transition-fast);
}

.faq-question:hover {
  background: #f8f9fa;
}

.faq-icon {
  font-size: 1.5rem;
  color: var(--primary-color);
  transition: var(--transition-fast);
}

.faq-answer {
  padding: 0 1.5rem 1.5rem;
  color: var(--text-dark);
  opacity: 0.8;
  line-height: 1.7;
}

.faq-answer p {
  margin: 0;
}

/* FAQ Answer Transition */
.faq-answer-enter-active,
.faq-answer-leave-active {
  transition: all 0.3s ease;
}

.faq-answer-enter-from,
.faq-answer-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Success Modal */
.success-modal {
  text-align: center;
  padding: 3rem;
  max-width: 400px;
}

.success-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.success-modal h3 {
  margin-bottom: 1rem;
  color: var(--text-dark);
}

.success-modal p {
  margin-bottom: 2rem;
  color: var(--text-dark);
  opacity: 0.8;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 2rem;
}

.modal-content {
  background: white;
  border-radius: var(--border-radius);
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

/* Modal Transitions */
.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .contact-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
}

@media (max-width: 768px) {
  .contact-content {
    gap: 2rem;
  }

  .info-item {
    padding: 1rem;
  }

  .social-links {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 1rem;
  }

  .map-placeholder {
    height: 300px;
  }

  .faq-question {
    padding: 1rem;
    font-size: 1rem;
  }

  .faq-answer {
    padding: 0 1rem 1rem;
  }

  .success-modal {
    padding: 2rem;
  }
}

@media (max-width: 480px) {
  .social-links {
    flex-direction: column;
  }

  .map-placeholder {
    height: 250px;
  }

  .map-icon {
    font-size: 2rem;
  }
}
</style>
